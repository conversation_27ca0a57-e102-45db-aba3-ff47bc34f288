  �    �            �                     /   ?   R   ^   j   y   �   �   �   �   �         "  V  �  �  �    K  orientation id 

background layout_width 

layout_height layout_marginTop 		scaleType 		minHeight fillViewport 

layout_weight layout_behavior layout_constraintEnd_toEndOf   layout_constraintStart_toStartOf layout_constraintTop_toTopOf 		srcCompat WebView android app 11com.ivini.carly2.ui.core.imageView.CarlyImageView 55com.ivini.carly2.ui.core.layout.CarlyConstraintLayout 11com.ivini.carly2.ui.core.layout.CarlyLinearLayout //com.ivini.carly2.ui.core.layout.CarlyScrollView ''http://schemas.android.com/apk/res-auto **http://schemas.android.com/apk/res/android style  � D   � � � � � � @z�GWko�        ����              ����       �      ��������                  ����  W       ����  ����      ����  ����      ����  ����   
   ����  @3 L      ��������                  ����     ����   ����  � �      ��������                  ����  ����      ����  ����      ����           ����            ����         
   ����            ����  �       ��������    `      ��������                  ����  ����      ����  ����   
   ����       `      ��������                  ����           ����  ����   	   ����    �?       ��������    `   $   ��������                  ����           ����  ����   	   ����    �?    $   ��������    t   *   ��������                 ����  �	      ����           ����  ����   	   ����    �?    *   ��������    t   0   ��������                  ����           ����  ����   	   ����    �?      ����      0   ��������          ��������    �   9   ��������                 ����  �	      ����  ����      ����  ����      ����  �      ����  �    
   ����          9   ��������          ��������          ��������          ����             ����      