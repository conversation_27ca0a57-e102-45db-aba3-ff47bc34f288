  $    �            �                    +   <   A   N   [   j   z   �   �   �   �   
  )  :  A  P  Z  ~  �  �  �    E  theme textSize 		textStyle 		textColor layout_gravity id 

background 

visibility layout_width 

layout_height text ""layout_constraintBottom_toBottomOf layout_constraintEnd_toEndOf   layout_constraintStart_toStartOf layout_constraintTop_toBottomOf layout_constraintTop_toTopOf titleTextColor View ViewSwitcher android !!androidx.appcompat.widget.Toolbar app 55com.ivini.carly2.ui.core.layout.CarlyConstraintLayout //com.ivini.carly2.ui.core.textView.CarlyTextView ''http://schemas.android.com/apk/res-auto **http://schemas.android.com/apk/res/android style    � L     � � � � � � � � � OQWkno>        ����              ����       L      ��������                 ����  = ����   ����  k �      ��������                  ����  �      ����  	      ����  ����   	   ����         ����            ����    �      ��������                  ����          ����           ����         ����           ����  ����   	   ����  ����   
   ����  �       ��������          ��������    �       ��������                 ����  F	      ����        	   ����           ����            ����         
   ����            ����  	        ��������    �   +   ��������     	            ����  9	      ����  �      ����           ����        	   ����           ����            ����         
   ����            ����          +   ��������          ��������          ����             ����      