  l    �            �              
      '   6   F   V   {   �   �   �   �       0  F  P  t  �  �  �    5  ?  theme id 

background 

visibility layout_width 

layout_height 

layoutManager ""layout_constraintBottom_toBottomOf layout_constraintEnd_toEndOf   layout_constraintStart_toStartOf layout_constraintTop_toBottomOf layout_constraintTop_toTopOf subtitleTextAppearance title titleTextColor LinearLayoutManager android !!androidx.appcompat.widget.Toolbar ))androidx.recyclerview.widget.RecyclerView app 55com.ivini.carly2.ui.core.layout.CarlyConstraintLayout ''http://schemas.android.com/apk/res-auto **http://schemas.android.com/apk/res/android include layout � D     � � � � � CQWkno�3>        ����              ����       L      ��������                  ����  ����      ����  ���� �      ��������                  ����  �      ����  	      ����  ����      ����         ����            ����  �   
   ����  -4      ����          ��������    �      ��������                 ����  _	      ����  =       ����  ����      ����                         ����         
   ����  	       ��������    �   '   ��������     	            ����  J	      ����           ����           ����           ����            ����         	   ����            ����      ����   ����  �     '   ��������          ��������          ����             ����      