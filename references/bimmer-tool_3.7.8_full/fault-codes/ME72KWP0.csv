"0x01","Leakage diagnostic pump output stage"
"0x02","Solenoid valve DMTL power stage"
"0x03","Swapped lambda sensors"
"0x04","Lambda probe heating behind Kat (Bank2)"
"0x05","Lambda probe heating in front of the cat (Bank2)"
"0x0A","Lambda probe before cat"
"0x0C","Lambda sensor behind cat"
"0x0D","Lambda probe heating in front of the cat"
"0x0E","Lambda probe heating behind cat"
"0x0F","Lambda probe aging period"
"0x10","Lambda probe aging TV"
"0x11","Lambda sensor aging h. cat"
"0x12","Lambda probe 2 in front of the cat"
"0x14","Lambda probe 2 behind cat"
"0x15","Lambda probe aging period Bank2"
"0x16","Lambda probe aging TV Bank2"
"0x17","Lambda sensor aging h. Cat Bank2"
"0x18","LR adaptation multiplicative range2"
"0x19","LR adaptation multipl. area2 (bank2)"
"0x1A","LR adaptation multiplicative range1"
"0x1B","LR adaptation multipl. Area1 (Bank2)"
"0x1C","LR adaptation additive per time"
"0x1D","LR adaptation additive per time bank 2"
"0x1E","LR adaptation additive per ignition"
"0x1F","LR adaptation additive per ignition bank 2"
"0x20","Idle control faulty"
"0x21","camshaft control"
"0x22","Camshaft timing bank 2"
"0x27","EWS3.3 Tamper Protection"
"0x28","Catalyst Conversion"
"0x2D","Catalyst Conversion (Bank2)"
"0x32","misfire detection cyl.1,"
"0x33","misfire detection cylinder 5,"
"0x34","misfire detection cylinder 4,"
"0x35","misfire detection cylinder 8,"
"0x36","misfire detection cylinder 6,"
"0x37","misfire detection cyl.3,"
"0x38","misfire detection cyl.7,"
"0x39","misfire detection cyl.2,"
"0x3E","misfire detection, cumulative error,"
"0x50","secondary air system"
"0x51","Secondary air system Bank2"
"0x52","secondary air valve"
"0x54","Final stage relay secondary air pump"
"0x55","Secondary air valve final stage"
"0x5D","Tank ventilation functional check"
"0x62","Tank ventilation valve final stage"
"0x65","Monitoring torque comparison level 2"
"0x66","Interface MFL"
"0x67","monitoring computer function"
"0x68","clutch switch"
"0x69","RAM test failed"
"0x6A","switch brake"
"0x6B","ROM test failed"
"0x6C","Reset of computer monitoring"
"0x6D","battery voltage"
"0x6E","Torque limitation level 1"
"0x6F","crankshaft sensor"
"0x70","fiducial marker"
"0x71","camshaft sensor"
"0x72","Camshaft sensor bank 2"
"0x73","Hot film air mass meter"
"0x75","DK potentiometer"
"0x76","Throttle Potentiometer 1"
"0x77","Throttle pot 2"
"0x78","vehicle speed"
"0x79","Wheel sensor error"
"0x7B","engine temperature"
"0x7C","intake air temperature"
"0x7D","Temperature sensor radiator outlet"
"0x82","DK position monitoring"
"0x83","DK controller control range"
"0x84","DK controller control"
"0x85","Spring test DK actuator"
"0x86","Check lower stop"
"0x87","DK controller error in amplifier adjustment."
"0x88","Test emergency air point"
"0x8B","Thermostat stuck"
"0x8C","End stage thermostat map cooling"
"0x8D","Motor fan output stage"
"0x8E","End stage exhaust flap"
"0x94","EWS3.3 DME-EWS interface"
"0x96","EV1 from cyl. 1"
"0x97","EV2 from cyl. 5"
"0x98","EV3 from cyl. 4"
"0x99","EV4 from cyl. 8th"
"0x9A","EV5 from cyl. 6"
"0x9B","EV6 from cyl. 3"
"0x9C","EV7 from cyl. 7"
"0x9D","EV8 from cyl. 2"
"0xA3","Diagnosis DK/HFM adjustment"
"0xA4","pressure sensor environment"
"0xA5","Output stage VANOS"
"0xA6","Output stage VANOS Bank 2"
"0xA7","Power stage fuel pump relay"
"0xA8","power amplifier MIL"
"0xAA","End stage air conditioning compressor release to air conditioning SG"
"0xB7","LDP diagnosis"
"0xB8","LDP system"
"0xB9","LDP module"
"0xBA","DM-TL pump motor power stage"
"0xBB","DM-TL small leak (0.5mm)"
"0xBC","DM-TL fine leak (1.0mm)"
"0xBD","DM TL module"
"0xCC","EWS3.3 variable code storage"
"0xD2","knock sensor1"
"0xD3","knock sensor2"
"0xD4","knock sensor3"
"0xD5","knock sensor4"
"0xD6","Knock control zero test"
"0xD7","Knock control offset"
"0xD8","Knock control test pulse"
"0xDB","CAN timeout TCU"
"0xDC","CAN timeout EGS"
"0xDD","CAN timeout ASC/DSC"
"0xDE","CAN timeout instrument cluster"
"0xDF","CAN timeout ACC"
"0xE0","Plausibility MSR intervention"
"0xE1","Plausibility of ACC intervention"
"0xE2","Plausibility tank filling level"
"0xE5","Comparison supplies PWG"
"0xE6","pedal sensor"
"0xE7","Pedal value sensor Poti1"
"0xE8","Pedal value sensor Poti2"
"0xE9","Start automatic output stage"
"0xEA","Automatic start input"
"0xED","automatic start"
"0xXY","unknown error location"
