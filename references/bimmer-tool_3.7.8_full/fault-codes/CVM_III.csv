"0x01", "Coding data record invalid, not coded".
"0x02", "Coding implausible, assignment CVM II or III".
"0x03", "Checksum error ROM"
"0x04", "Checksum error EEPROM"
"0x05", "Checksum error in the coding data"
"0x06", "RAM check error"
"0x07", "Watchdog error"
"0x08", "Opcode error"
"0x09", "Clock monitor error"
"0x0A", "Communication error K-bus interface"
"0x10", "Supply to VSW in vehicle out of tolerance".
"0x11", "Power supply to VSW on convertible top out of tolerance".
"0x12", "Control button 'Open' permanently active".
"0x13", "Control button 'Close' permanently active".
"0x14", "Temperature sensor input, permanently to ground".
"0x15", "Temperature sensor input, no sensor connected".
"0x16", "Potentiometer main column, disconnected"
"0x17", "Potentiometer main column, to ground"
"0x18", "Potentiometer clamping yoke, torn off"
"0x19", "Potentiometer, clamping yoke, to ground
"0x1A", "VSW 1.2, input voltage >5 volts"
"0x1B", "VSW 1.2, input voltage undefined"
"0x1C", "VSW 1.2, input to ground"
"0x1D", "VSW 4.1, input voltage >5 volts"
"0x1E", "VSW 4.1, input voltage undefined"
"0x1F", "VSW 4.1, input to ground"
"0x20", "VSW 4.2, input voltage >5 Volt"
"0x21", "VSW 4.2, input voltage undefined
"0x22", "VSW 4.2, input to ground
"0x23", "VSW 7.1, input voltage >5 volts
"0x24", "VSW 7.1, input voltage undefined
"0x25", "VSW 7.1, input to ground
"0x26", "VSW 7.2, input voltage >5 volts"
"0x27", "VSW 7.2, input voltage undefined
"0x28", "VSW 7.2, input to ground
"0x29", "VSW 8, input voltage >5 Volt"
"0x2A", "VSW 8, input voltage undefined"
"0x2B", "VSW 8, input to ground
"0x2C", "VSW 9, input voltage >5 volts"
"0x2D", "VSW 9, input voltage undefined"
"0x2E", "VSW 9, input to ground"
"0x2F", "VSW HTOP, input voltage >5 volts"
"0x30", "VSW HTOP, input voltage undefined".
"0x31", "VSW HTOP, input to ground".
"0x40", "Unlock, motor current H-bridge too high, short circuit"
"0x41", "Lock, motor current H-bridge too high, short-circuit
"0x42", "Short-circuit motor bridge or motor to Ub+".
"0x43", "Open load during unlocking"
"0x44", "Open load during locking
"0x45", "V4SBA, not connected"
"0x46", "V4SBA, short circuit to ground"
"0x47", "V4SBA, short circuit to UBatt"
"0x48", "V3SBE, not connected"
"0x49", "V3SBE, short circuit to ground".
"0x4A", "V3SBE, short circuit to UBatt"
"0x4B", "Outputs V4SBA and V2_HAUPT, connected by short circuit"
"0x4C", "V2_HAUPT, not connected"
"0x4D", "V2_HAUPT, short circuit to ground"
"0x4E", "V2_HAUPT, short circuit to UBatt"
"0x50", "V5GO, not connected"
"0x51", "V5GO, short circuit to ground".
"0x52", "V5GO, short circuit to UBatt"
"0x67", "Wind Run does not open"
"0x68", "Wind Run Does Not Close"
"0x69", "RPUMPEA, not connected"
"0x6A", "RPUMPEA, short circuit to ground"
"0x6B", "RPUMPEA, short circuit to UBatt"
"0x6C", "RPUMPEZ, not connected"
"0x6D", "RPUMPEZ, short circuit to ground".
"0x6E", "RPUMPEZ, short circuit to UBatt".
"0xFF", "unknown error location"