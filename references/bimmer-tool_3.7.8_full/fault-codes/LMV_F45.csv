0x021900, energy saving mode active
0x021908,Coding: ECU not coded
0x021909,Coding: Error during coding data transaction
0x02190A,Coding: Coding data signature error
0x02190B,Coding: Coding data vehicle mismatch
0x02190C,Coding: Invalid data during coding data transaction
0x02FF19,"Dummy DTC within component range, for testing purpose only"
0x44003C,Undervoltage identified
0x44003D,Overvoltage identified
0x440200, wheel speed status implausible
0x440201, wheel speed status unsecured
0x440202, status of actual wheel speed implausible
0x440203, Status of actual wheel speed unsecured
0x440204, status target torque implausible
0x440205,Status target torque not secured
0x440206,Status target torque not checked
0x440207,Status target torque implausible or not secured
0x440208,Status relative time implausible
0x440209,Status relative time not secured
0x440300,Pump motor: Pin A or Pin B short circuit to ground
0x440301,Pump motor: Pin A or Pin B short circuit to plus
0x440302,Pump motor: line break
0x440303,Pump motor: short circuit between pin A and pin B
0x440304,Pump motor: current too low
0x440305,Pump motor: defective
0x440306,Control unit processor: temperature too high
0x440307,Control unit processor: temperature too low
0x440308,Temperature sensor processor: Short circuit
0x440309,Temperature sensor processor: Line break
0x440316,Clutch: Calculated temperature too high
0x440317,Pump motor: General group error
0x440318,Analog Digital Converter: Error
0x440330,Control unit: Parameters for pump control incorrect
0x440331,Control unit: Software not compatible with EEPROM
0x440332,Control unit: Software or hardware incompatible
0x440333,Pump motor: Calibration missing
0x440334,Pump motor: Calibration outside the valid range
0x440335,Control unit: EEPROM checksum error
0x440337,Control unit: General parameters missing or not compatible
0x440339,Safety function
0x44033A,Control unit: Memory test error - Unrecoverable
0x44033B,Control unit: Memory test error - Recoverable
0x440350,Supply voltage: Line interruption
0x440351,Terminal 15: Undervoltage
0x440352,Terminal 15: Overvoltage
0xCF450B,B2-CAN physical bus error
0xCF4514,B2 -CAN Control Module Bus OFF
0xCF4BFF,Dummy-DTC (network range) for testing purpose only
0xCF5400,"Message (crankshaft torque 1, 0xA5) missing, receiver LMV, sender DME1"
0xCF5401,"Message (target torque, 0x0E5) missing, receiver LMV, transmitter DSC"
0xCF5402,"Signal (crankshaft torque 1, 0xA5) invalid, sender DME1"
0xCF5403,"Message (relative time BN2020, 0x328) missing, receiver LMV, sender KOMBI"
0xCF5404,"Signal (relative time, 0x328) invalid, sender KOMBI" 0xCF5406,"Message (Wheel speed not secured, 0x254) missing, LMV receiver, DSC sender"
0xCF5408,"Message (Odometer reading, 0x330) missing, LMV receiver, KOMBI sender"
0xCF5409,"Message (Terminals, 0x12F) missing, LMV receiver , Sender BDC"
0xCF5412,"Message (vehicle speed, 0x1A1) missing, Receiver LMV, sender DSC"
0xCF5413,"Message (drivetrain data 2, 0x3F9) missing, receiver LMV, sender DME1"
0xCF5414,"Signal (drivetrain data 2, 0x3F9) invalid, sender DME1"
0xCF5421,"Message (vehicle status, 0x3A0) missing , receiver LMV, sender ZGW"
0xCF5422,"Message (actual wheel speed qualifier, 0x258) missing, receiver LMV, sender DSC"
0xCF5480,"Signal (target torque, 0x0E5) invalid, sender DSC"
0xCF5486,"Signal (actual wheel speed unsecured , 0x254) invalid, transmitter DSC"
0xCF5488,"Signal (mileage, 0x330) invalid, transmitter KOMBI"
0xCF5489,"Signal (terminals, 0x12F) invalid, sender BDC"
0xCF5493,"Signal (vehicle speed, 0x1A1) invalid, sender DSC"
0xCF54A1,"Signal (vehicle status, 0x3A0) invalid, sender ZGW"
0xCF54A2,"Signal ( If the wheel speed qualifier (0x258) is invalid, DSC sender"
0xCF54C1,"Message (target torque, 0x0E5) not current, LMV receiver, DSC sender"
0xCF54C2,"Message (vehicle speed, 0x1A1) not current, LMV receiver, DSC sender"
0xCF5501 ,"Message (target torque, 0x0E5) checksum incorrect, receiver LMV, sender DSC"
0xCF5502,"Message (vehicle speed, 0x1A1) checksum incorrect, receiver LMV, transmitter DSC"
0xFFFFFF, unknown error location