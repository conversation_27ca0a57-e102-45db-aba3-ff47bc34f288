"0x2711","CDKLDPE - Leakage diagnostic pump output stage"
"0x2712","CDKDMMVE - Control solenoid valve DM-TL"
"0x2713","CDKLSVV - Swapped oxygen sensors"
"0x2714","CDKHSH2 - Lambda probe heater behind Kat (Bank2)"
"0x2715","CDKHSV2 - Lambda probe heating in front of the cat (Bank2)"
"0x2716","CDKHSHE - Activation of heating probe behind cat"
"0x2717","CDKHSHE2 - Activation of heating probe behind cat (Bank2)"
"0x2718","CDKNZ - speed sensor tooth error"
"0x2719","CDKDGZ - Speed encoder period time"
"0x271A","CDKLSV - Lambda probe before cat"
"0x271B","CDKLSV - Lambda probe before cat"
"0x271C","CDKLSH - Lambda sensor behind cat"
"0x271D","CDKHSV - Lambda probe heating before cat"
"0x271E","CDKHSH - Lambda probe heating behind cat"
"0x271F","CDKLATP - Lambda sensor aging period"
"0x2720","CDKLATV - Lambda probe aging TV"
"0x2721","CDKLASH - Lambda sensor aging h. cat"
"0x2722","CDKLSV2 - Lambda probe 2 before cat"
"0x2724","CDKLSH2 - Lambda probe 2 behind cat"
"0x2725","CDKLATP2 - Lambda probe aging period bank2"
"0x2726","CDKLATV2 - Lambda probe aging TV Bank2"
"0x2727","CDKLASH2 - Lambda sensor aging h. Cat Bank2"
"0x2728","CDKFRAO - Adaptation multiplicative range2"
"0x2729","CDKFRAO2 - Adaptation multipl. area2 (bank2)"
"0x272A","CDKFRAU - Adaptation multiplicative area1"
"0x272B","CDKFRAU2 - Adaptation multipl. Area1 (Bank2)"
"0x272C","CDKRKAT - Adaptation additive per time"
"0x272D","CDKRKAT2 - Adaptation additive per time (Bank2)"
"0x272E","CDKRKAZ - Adaptation additive per ignition"
"0x272F","CDKRKAZ2 - Adaptation additive per ignition Bank2"
"0x2730","CDKLLRP - idle control failed"
"0x2731","CDKENWS - camshaft control inlet - VANOS"
"0x2732","CDKENWS2 - NW Control Intake B2 (8cyl)/Exhaust (4cyl)"
"0x2733","CDKSNWDG - NW-KW sync error"
"0x2734","CDKLE - TPS/MAF plausibility"
"0x2735","CDKLE2 -TPS/MAF plausibility Bank2"
"0x2736","CDKDVPWK - Throttle Controller PWM short test"
"0x2737","CDKWFS - Immobilizer Tamper Protection"
"0x2738","CDKKAT - Catalyst Conversion"
"0x2739","CDKKATSP - Catalyst Conversion LSU"
"0x273A","CDKKATSP2 - Catalyst Conversion LSU Bank2"
"0x273B","CDKDVPWL - Throttle Controller PWM Long Test"
"0x273C","CDKDVRD - Throttle Position Controller Differential"
"0x273D","CDKKAT2 - Catalyst Conversion (Bank2)"
"0x273E","CDKATNV - signal temperature sensor flue gas 1"
"0x273F","CDKATNV2 - signal temperature sensor flue gas 2"
"0x2740","CDKFPV - Pedal value sensor 1 duration"
"0x2741","CDKFPV2 - Pedal Sensor 2 Duration"
"0x2742","CDKMD00 - misfire detection cylinder 1"
"0x2743","CDKMD01 - misfire detection cyl.3"
"0x2744","CDKMD02 - misfire detection cylinder 4"
"0x2745","CDKMD03 - misfire detection cylinder 2"
"0x2746","CDKMD04 - misfire detection cyl."
"0x2747","CDKMD05 - misfire detection cyl."
"0x2748","CDKMD06 - misfire detection cyl"
"0x2749","CDKMD07 - misfire detection cyl"
"0x274A","CDKMD08 - misfire detection cyl"
"0x274B","CDKMD09 - misfire detection cyl"
"0x274C","CDKMD10 - misfire detection cyl"
"0x274D","CDKMD11 - misfire detection cyl"
"0x274E","CDKMD - misfire detection, sum error"
"0x274F","CDKMDK - dropouts, sum error, service relevant"
"0x2752","CDKFPPH - pedal value transmitter half plausibility"
"0x2753","CDKDZKU0 - Monitoring ignition coil 1"
"0x2754","CDKDZKU1 - monitoring ignition coil 3"
"0x2755","CDKDZKU2 - monitoring ignition coil 4"
"0x2756","CDKDZKU3 - monitoring ignition coil 2"
"0x2757","CDKDZKU4 - monitoring ignition coil"
"0x2758","CDKDZKU5 - Monitoring ignition coil"
"0x2759","CDKDZKU6 - Monitoring ignition coil"
"0x275A","CDKDZKU7 - Monitoring ignition coil"
"0x275B","CDKDZKU8 - Monitoring ignition coil"
"0x275C","CDKDZKU9 - monitoring ignition coil"
"0x275D","CDKDZKU10 - Monitoring ignition coil"
"0x275E","CDKDZKU11 - Monitoring ignition coil"
"0x275F","CDKFPD - Pedal value sensor defective"
"0x2760","CDKSLS - Secondary Air System"
"0x2761","CDKSLS2 - Secondary Air System Bank2"
"0x2762","CDKSLV - Secondary Air Valve"
"0x2763","CDKSLV2 - secondary air valve bank2"
"0x2764","CDKSLPE - Activation of the secondary air pump relay"
"0x2765","CDKSLVE - Actuation of secondary air valve"
"0x2766","CDKPHS - Phaser1 signal duration"
"0x2767","CDKPHS2 - Phase encoder2 signal duration"
"0x2768","CDKPHP - Phase encoder1 position error"
"0x2769","CDKDVEFO - spring test DK actuator opening spring"
"0x276A","CDKSGA - control unit selection"
"0x276B","CDKSLVE2 - secondary air valve final stage Bank2"
"0x276C","CDKPHP2 - Phase encoder2 position error"
"0x276D","CDKTES - tank ventilation functional check"
"0x276E","CDKTES2 - tank ventilation functional check Bank2"
"0x276F","CDKSLMM - secondary mass air flow sensor faulty"
"0x2770","CDKSLM - secondary air mass faulty"
"0x2771","CDKSLG - Secondary air system blocked"
"0x2772","CDKTEVE - Activation tank ventilation valve"
"0x2773","CDKTEVE2 - tank ventilation valve final stage Bank2"
"0x2774","CDKMEMUM - Monitoring of cyclic error storage"
"0x2775","CDKUFMV - engine torque monitoring level 2"
"0x2776","CDKMFL - Interface multifunction steering wheel"
"0x2777","CDKUFSKA - monitoring computer function"
"0x2778","CDKKUPPL - Switch clutch"
"0x2779","CDKURRAM - SG Self Test RAM"
"0x277A","CDKBREMS - Brake switch"
"0x277B","CDKURROM - SG Self Test ROM"
"0x277C","CDKURRST - SG self-test reset"
"0x277D","CDKUB - battery voltage"
"0x277E","CDKMDB - Torque limitation level 1"
"0x277F","CDKN - crankshaft sensor"
"0x2780","CDKBM - Reference Mark Generator"
"0x2781","CDKPH - camshaft sensor inlet"
"0x2782","CDKPH2 - Camshaft sensor outlet"
"0x2783","CDKLM - hot film air mass meter"
"0x2784","CDKTHM - Thermostat diagnosis THM"
"0x2785","CDKDK - DK potentiometer"
"0x2786","CDKDK1P - Throttle Potentiometer 1"
"0x2787","CDKDK2P - Throttle Potentiometer 2"
"0x2788","CDKVFZ - vehicle speed"
"0x2789","CDKSWE - Rough road detection"
"0x278A","CDKTUM - ambient temperature"
"0x278B","CDKTM - engine temperature"
"0x278C","CDKTA - intake air temperature"
"0x278D","CDKTKA - temperature sensor radiator outlet"
"0x278E","CDKDDSS - Intake Manifold Differential Pressure Sensor"
"0x278F","CDKGLOR - LowRange signal implausible"
"0x2790","CDKTGET - Transmission Temperature"
"0x2791","CDKDVET - swap detection without adaptation"
"0x2792","CDKDVEL - Throttle Position Monitoring"
"0x2793","CDKDVER - DK controller control range"
"0x2794","CDKDVEE - DK actuator control"
"0x2795","CDKDVEF - spring test DK actuator closing spring"
"0x2796","CDKDVEU - Throttle valve - Check lower stop"
"0x2797","CDKDVEV - DK controller error in amplifier adjustment"
"0x2798","CDKDVEN - Throttle valve - Test emergency air point"
"0x2799","CDKDVEUB - DV adaptation aborted due to environmental conditions"
"0x279A","CDKDVEUW - Throttle valve adaptation - abort on relearn"
"0x279B","CDKTHS - Thermostat stuck"
"0x279C","CDKETS - Activation of thermostat mapped cooling"
"0x279D","CDKMLE - Motor fan control"
"0x279E","CDKAKRE - final stage exhaust flap"
"0x279F","CDKLUEA - power stage fanA"
"0x27A0","CDKELS - E-Box fan control"
"0x27A1","CDKSLMM2 - Secondary Mass Air Flow Sensor2 faulty"
"0x27A2","CDKTMP - LR engine temperature sensor"
"0x27A3","CDKCHDEV2 - CAN timeout HDEV2 SG"
"0x27A4","CDKDWA - EWS3.3 interface EWS-DME"
"0x27A6","CDKEV1 - Activation of injector 1"
"0x27A7","CDKEV2 - actuation of injector 3"
"0x27A8","CDKEV3 - control of injector 4"
"0x27A9","CDKEV4 - control of injector 2"
"0x27AA","CDKEV5 - activation of injection valve"
"0x27AB","CDKEV6 - fuel injector control"
"0x27AC","CDKEV7 - activation of injection valve"
"0x27AD","CDKEV8 - activation of injection valve"
"0x27AE","CDKEV9 - activation of injection valve"
"0x27AF","CDKEV10 - Injector control"
"0x27B0","CDKEV11 - activation of injection valve"
"0x27B1","CDKEV12 - activation of injection valve"
"0x27B3","CDKEGFE - Diagnosis DK/HFM adjustment"
"0x27B4","CDKDSU - Pressure sensor environment"
"0x27B5","CDKENWSE - actuation of intake VANOS"
"0x27B6","CDKENWSE2 - actuation of inlet VANOS Bank2"
"0x27B7","CDKKPE - Activation of fuel pump relay"
"0x27B8","CDKPDDSS - Differential pressure sensor plausibility"
"0x27B9","CDKVLS1 - BLS/BTS plausibility"
"0x27BA","CDKVLS2 - End stage air conditioning compressor release to air conditioning SG"
"0x27BB","CDKANWS - camshaft control outlet VANOS"
"0x27BC","CDKANWS2 - camshaft control exhaust VANOS Bank2"
"0x27BD","CDKANWSE - activation of exhaust VANOS"
"0x27BE","CDKANWSE2 - Output stage exhaust VANOS Bank2"
"0x27BF","CDKPH3 - camshaft sensor inlet Bank2"
"0x27C0","CDKPH4 - camshaft sensor outlet Bank2"
"0x27C1","CDKPHM - Master camshaft encoder"
"0x27C2","CDKKOSE - actuation of air conditioning compressor relay"
"0x27C3","CDKTOENS - Oil level sensor signal (TOENS)"
"0x27C6","CDKTESK - LDP diagnosis fine leak"
"0x27C7","CDKTESG - LDP diagnosis major leak"
"0x27C8","CDKLDP - LDP System"
"0x27C9","CDKLDP - Leak Diagnostic Module"
"0x27CA","CDKDMPME - Control DM-TL pump motor"
"0x27CB","CDKDMTKNM - DM-TL fine leak (0.5mm) MIL off"
"0x27CC","CDKDMTK - DM-TL Coarse & fine leak"
"0x27CE","CDKUFRLIP - load sensor monitoring"
"0x27CD","CDKDMTL - DM-TL module"
"0x27CF","CDKZZ1 - Ignition time cyl1"
"0x27D0","CDKZZ2 - ignition time cyl3"
"0x27D1","CDKZZ3 - ignition time cyl4"
"0x27D2","CDKZZ4 - ignition time cyl2"
"0x27D5","CDKLLR - idle control faulty"
"0x27D6","CDKISA2 - power stage idle control actuator closed"
"0x27D7","DDKISA1 - output stage idle control actuator up"
"0x27D8","CDKVP - vacuum pump fault"
"0x27D9","CDKDHDMTE - Output stage DM-TL heating"
"0x27DA","CDKDGEN - generator error"
"0x27DC","CDKWCA - EWS3.3 rolling code storage"
"0x27E1","CDKUFSPSC - Pedal Sensor Monitoring"
"0x27E2","CDKKS1 - knock sensor1"
"0x27E3","CDKKS2 - Knock Sensor2 Bank1"
"0x27E4","CDKKS3 - Knock Sensor3"
"0x27E5","CDKKS4 - knock sensor4"
"0x27E6","CDKKRNT - Knock Control Zero Test"
"0x27E7","CDKKROF - Knock Control Offset"
"0x27E8","CDKKRTP - Knock Control Test Pulse"
"0x27E9","CDKKRNT2 - Knock Control Zero Test Bank2"
"0x27EA","CDKCHDEV - CAN timeout HDEV"
"0x27EB","CDKCTXU - CAN timeout TCU"
"0x27EC","CDKCGE - CAN timeout EGS"
"0x27ED","CDKCAS - CAN timeout ASC/DSC"
"0x27EE","CDKCINS - CAN timeout instrument cluster"
"0x27EF","CDKCACC - CAN ACC signal error"
"0x27F0","CDKAS - MSR intervention plausibility"
"0x27F1","CDKACC - Plausibility of ACC intervention"
"0x27F3","CDKCVVT - VVT control unit CAN timeout"
"0x27F4","CDKCVVT2 - CAN timeout VVT control unit Bank2"
"0x27F5","CDKCDMEL - CAN timeout DME control unit"
"0x27F6","CDKFPP - pedal value transmitter"
"0x27F7","CDKFP1P - Poti1 pedal value sensor"
"0x27F8","CDKFP2P - Poti2 pedal value sensor"
"0x27F9","CDKSTAE - automatic start control"
"0x27FA","CDKSTS - automatic start-stop input"
"0x27FB","CDKGLFE - power stage controlled airflow"
"0x27FD","CDKSTA - automatic start-stop"
"0x27FE","CDKKROF2 - Knock Control Offset Bank2"
"0x27FF","CDKKRTP2 - Knock Control Test Pulse Bank2"
"0x280A","CDKNWKW - Allocation of camshaft to crankshaft"
"0x280D","CDKSGB- control unit monitoring"
"0x280E","CDKSGC control unit monitoring"
"0x280F","CDKNWS - camshaft control"
"0x2810","CDKUFNC - Engine Speed ​​Monitor"
"0x2811","CDKCANB - Local CAN Bus Off"
"0x2812","CDKTOL - Oil Temperature"
"0x2813","CDKUFSGA - control unit monitoring group A"
"0x2814","CDKUFSGB - control unit monitoring group B"
"0x2815","CDKUFSGC - control unit monitoring group C"
"0x2816","CDKUFNC - engine speed monitor"
"0x281E","CDKSUE - Control DISA"
"0x281F","CDKSUR - DISA storage confirmation"
"0x2823","CDKHSVSA - Pre-catalyst lambda probe heater (shear voltage)"
"0x2824","CDKHSVSA2 - heating lambda probe v. Cat Bank2 (shear stress)"
"0x2825","CDKLAVH - Lambda sensor aging h. Kat (VL test)"
"0x2826","CDKLAVH2 - Lambda sensor aging h. Kat Bank2 (VL test)"
"0x2828","CDKCARS - CAN ARS signal error"
"0x2829","CDKCCAS - CAN CAS signal error"
"0x282A","CDKCIHKA - CAN IHKA signal error"
"0x282B","CDKCPWML - CAN PWML signal error"
"0x282C","CDKCSZL - CAN SZL signal error"
"0x282E","CDKBWF - PWG Movement"
"0x2832","CDKASR - ASR moment plausibility"
"0x2833","CDKCAS - Plausibility CAS"
"0x2834","CDKIHKA - Plausibility IHKA"
"0x2835","CDKPWML - plausibility PWML"
"0x2836","CDKSZL - SZL plausibility"
"0x2837","CDKEMF - Plausibility EMF"
"0x2838","CDKAAVE - power amplifier AAV"
"0x2839","CDKAAV - AAV functionality"
"0x283A","CDKOEZS - Oil quality sensor error"
"0x283B","CDKNWSE2 - camshaft control output stage Bank2"
"0x283C","CDKNWSE - camshaft control output stage"
"0x283D","CDKCANA - PT - CAN Bus Off"
"0x283E","CDKVVTE - VVT Enable control"
"0x283F","CDKPOELS - Plausibility oil pressure switch"
"0x2841","CDKLUVE - air-enclosed injector control"
"0x284F","CDKVAT -"
"0x2850","CDKDVFFS - VVT Guide Sensor"
"0x2851","CDKDVFFS2 - VVT Guide Sensor (Bank2)"
"0x2852","CDKDVFRS - VVT reference sensor"
"0x2853","CDKDVFRS2 - VVT Reference Sensor (Bank2)"
"0x2854","CDKDVPLA - VVT sensor plausibility check"
"0x2855","CDKDVPLA2 - VVT sensor plausibility check (Bank2)"
"0x2856","CDKDVUSE - VVT supply voltage sensor"
"0x2857","CDKDVUSE2 - VVT Supply Voltage Sensor (Bank2)"
"0x2858","CDKDVLRN - VVT learn stop"
"0x2859","CDKDVLRN2 - VVT Learn Stop (Bank2)"
"0x285A","CDKDVSTE - VVT Actuator Monitoring"
"0x285B","CDKDVSTE2 - VVT Actuator Monitor (Bank2)"
"0x285C","CDKDVCAN - VVT CAN communication"
"0x285D","CDKDVCAN2 - VVT CAN Communication (Bank2)"
"0x285E","CDKDVFSG - VVT control unit internal error"
"0x285F","CDKDVFSG2 - VVT control unit internal error (Bank2)"
"0x2860","CDKDVEST - VVT control"
"0x2861","CDKDVEST2 - VVT Activation (Bank2)"
"0x2862","CDKDVULV - VVT power supply"
"0x2863","CDKDVULV2 - VVT Power Supply (Bank2)"
"0x2865","CDKDVPMN - VVT limp home power limitation"
"0x2866","CDKDVAN - VVT stops learning required"
"0x2867","CDKDVOVL - VVT System Overload"
"0x2868","CDKDVOVL2 - VVT System Overload (Bank2)"
"0x286F","CDKAGRE - EGR valve final stage"
"0x2870","CDKAGRF - EGR valve monitoring"
"0x2871","CDKAGRL - EGR valve position sensor"
"0x2872","CDKAGRV - Diagnosis EGR valve"
"0x2873","CDKHDEVEA1 - Power amplifier HDEV-SG1 Bank1"
"0x2874","CDKHDEVEA2 - Power amplifier HDEV-SG1 Bank2"
"0x2875","CDKHDEVEA3 - Power amplifier HDEV-SG1 Bank3"
"0x2876","CDKHDEVEB1 - Power amplifier HDEV-SG2 Bank1"
"0x2877","CDKHDEVEB2 - power amplifier HDEV-SG2 Bank2"
"0x2878","CDKHDEVEB3 - power amplifier HDEV-SG2 Bank3"
"0x2879","CDKATS4 - Signal exhaust gas temperature sensor 4"
"0x287A","CDKDSVE - pressure control valve final stage"
"0x287B","CDKATS3 - Signal exhaust gas temperature sensor 3"
"0x287C","CDKDSS - Intake Manifold Pressure Sensor"
"0x287D","CDKRDS - Rail pressure sensor signal"
"0x287E","CDKDSV - pressure control valve"
"0x287F","CDKDSKV - high pressure sensor test"
"0x2880","CDKAGRS - EGR system"
"0x2881","CDKBKE power stage twist generation controller"
"0x2882","CDKMSVE - final stage pressure control valve"
"0x2883","CDKHDR - Rail Pressure Control"
"0x2898","CDKLS1SH - Post-catalyst oxygen sensor bank 1: signal"
"0x28A0","CDKKUE - power stage fuel circuit switching"
"0x28C8","CDKFRST - Lambda Control Deviation"
"0x28C9","CDKFRST2 - Lambda control deviation Bank2"
"0x28D2","CDKDSL - pressure sensor boost pressure"
"0x28D3","CDKDSVLU - plausibility check of ambient to boost pressure"
"0x28D4","CDKLDE - boost pressure control valve actuation"
"0x28D5","CDKUVSE - Turbo recirculation valve final stage"
"0x28D6","CDKCOD - HO process error, encoding missing"
"0x28D7","CDKDGENBS - Generator communication"
"0x28D8","CDKNVRBUP - RAM backup error p"
"0x28D9","CDKEZH - Electric auxiliary heater"
"0x28DA","CDKCEZ - CAN Timeout Electric Auxiliary Heater"
"0x28DB","CDKMINHUB - Minhub adaptation limit exceeded several times"
"0x28DC","CDKDPL -"
"0x2906","CDKAGRE - EGR valve monitoring"
"0x2907","CDKAGRF - EGR valve monitoring"
"0x2972","CDKBKVPE - Activation of suction jet pump for brake booster"
"0xFFFF","unknown error location"
