import { dot } from 'node:test/reporters';
export { default as assert } from 'node:assert';
import { describe } from 'node:test';
export { after, afterEach, before, beforeEach, test } from 'node:test';

/**
 * Represents a CAN (Controller Area Network) message.
 *
 * @category CAN
 */
interface CanMessage {
    /**
     * The name of the CAN message.
     */
    name?: string;
    /**
     * The device associated with the CAN message.
     */
    device?: string;
    /**
     * The direction of the CAN message, either 'IN' for incoming or 'OUT' for outgoing.
     */
    dir: 'IN' | 'OUT';
    /**
     * The data payload of the CAN message.
     */
    data: Buffer;
    /**
     * The timestamp of when the CAN message was sent/recv.
     */
    ts?: number;
    /**
     * The identifier of the CAN message.
     */
    id: number;
    /**
     * The type of the CAN message.
     */
    msgType: CanMsgType;
    /**
     * Indicates whether the CAN message is simulated.
     * This property is optional.
     */
    isSimulate?: boolean;
    /**
     * The database name of the CAN message.
     */
    database?: string;
    /**
     * The children signals of the CAN message.
     * internal use
     */
    children?: {
        name: string;
        data: string;
    }[];
}
/**
 * Enumeration representing different CAN (Controller Area Network) ID types.
 *
 * @category CAN
 * @enum {string}
 * @readonly
 */
declare enum CAN_ID_TYPE {
    STANDARD = "STANDARD",
    EXTENDED = "EXTENDED"
}
/**
 * Enumeration representing different CAN (Controller Area Network) address types.
 *
 * @category CAN
 * @enum {string}
 * @readonly
 */
declare enum CAN_ADDR_TYPE {
    PHYSICAL = "PHYSICAL",
    FUNCTIONAL = "FUNCTIONAL"
}
/**
 * Enumeration representing different CAN (Controller Area Network) address formats.
 *
 * @category CAN
 * @enum {string}
 * @readonly
 */
declare enum CAN_ADDR_FORMAT {
    NORMAL = "NORMAL",
    FIXED_NORMAL = "NORMAL_FIXED",
    EXTENDED = "EXTENDED",
    MIXED = "MIXED",
    ENHANCED = "ENHANCED"
}
/**
 * Represents a CAN (Controller Area Network) message type.
 * @category CAN

 */
interface CanMsgType {
    /**
     * The type of CAN ID.
     */
    idType: CAN_ID_TYPE;
    /**
     * Indicates if Bit Rate Switching (BRS) is enabled.
     */
    brs: boolean;
    /**
     * Indicates if CAN FD (Flexible Data-rate) is used.
     */
    canfd: boolean;
    /**
     * Indicates if the message is a remote frame.
     */
    remote: boolean;
    /**
     * Optional unique identifier for the message.
     */
    uuid?: string;
}

interface VinInfo {
    vin: string;
    logicalAddr: number;
    eid: string;
    gid: string;
}
/**
 * @category DOIP
 */
interface EntityAddr extends VinInfo {
    nodeType?: 'node' | 'gateway';
    nodeAddr?: number;
    ta?: string;
    ip?: string;
    mcts?: number;
    ncts?: number;
    mds?: number;
    powerMode?: number;
    localPort?: number;
    sendSync?: boolean;
    udpLocalPort?: number;
    furtherAction?: number;
    syncStatus?: number;
}

/**
 * @category LIN
 */
declare enum LinDirection {
    SEND = "SEND",
    RECV = "RECV",
    RECV_AUTO_LEN = "RECV_AUTO_LEN"
}
/**
 * @category LIN
 */
declare enum LinMode {
    MASTER = "MASTER",
    SLAVE = "SLAVE"
}
/**
 * @category LIN
 */
declare enum LinChecksumType {
    CLASSIC = "CLASSIC",
    ENHANCED = "ENHANCED"
}
/**
 * @category LIN
 */
interface LinMsg {
    frameId: number;
    data: Buffer;
    direction: LinDirection;
    checksumType: LinChecksumType;
    checksum?: number;
    database?: string;
    device?: string;
    workNode?: string;
    name?: string;
    isEvent?: boolean;
    uuid?: string;
    ts?: number;
    /**
     * The children signals of the CAN message.
     * internal use
     */
    children?: {
        name: string;
        data: string;
    }[];
}

type DataType = 'NUM' | 'ARRAY' | 'ASCII' | 'UNICODE' | 'FLOAT' | 'DOUBLE' | 'FILE';
/**
 * @category UDS
 */
interface ServiceItem {
    id: string;
    name: string;
    serviceId: ServiceId;
    subfunc?: string;
    suppress?: boolean;
    autoSubfunc?: boolean;
    desc?: string;
    params: Param[];
    respParams: Param[];
}
interface Param {
    id: string;
    name: string;
    longName?: string;
    desc?: string;
    type: DataType;
    value: Buffer;
    phyValue: any;
    bitLen: number;
    bitPos?: number;
    meta?: {
        type: string;
    };
    deletable?: boolean;
    editable?: boolean;
}
/**
 * @category UDS
 */
type ServiceId = '0x10' | '0x11' | '0x27' | '0x28' | '0x29' | '0x3E' | '0x83' | '0x84' | '0x85' | '0x22' | '0x23' | '0x24' | '0x2A' | '0x2C' | '0x2E' | '0x3D' | '0x14' | '0x87' | '0x19' | '0x2F' | '0x31' | '0x34' | '0x35' | '0x36' | '0x37' | '0x38' | 'Job';

/**
 * @category UDS
 */
declare class SecureAccessDll {
    _ref: any;
    constructor(dllPath: string);
    /**
     * Generates a key with extended options.
     *
     * @param ipSeedArray - A buffer containing the seed array, for c: = ipSeedArray + iSeedArraySize
     * @param iSecurityLevel - The security level to be used.
     * @param ipVariant - A buffer containing the variant. for c: = ipVariant, size decide by vendor self
     * @param ipOptions - A buffer containing the options. for c: = ipOptions, size decide by vendor self
     * @param key - A buffer containing the input key.for c: = iopKeyArray + iMaxKeyArraySize
     * @returns A buffer containing the generated key. Return is Buffer, for c: = iopKeyArray, length = oActualKeyArraySize
     * @throws Will throw an error if the key generation fails.
     *
     * @example
     * ```typescript
     *
     * const dllPath=path.join(__dirname,'GenerateKeyExOpt.dll')
     * const sa=new SecureAccessDll(dllPath)
     *
     * const seed=sa.GenerateKeyExOpt(Buffer.from([1,2,3,4,5]),1,Buffer.from([1,2,3,4,5]),Buffer.from([1,2,3,4,5]),Buffer.from([1,2,3,4,5]))
     * ```
     *
     */
    GenerateKeyExOpt(ipSeedArray: Buffer, iSecurityLevel: number, ipVariant: Buffer, ipOptions: Buffer, key: Buffer): Buffer;
    /**
     * Generates a key with extended options.
     *
     * @param ipSeedArray - A buffer containing the seed array, for c: = ipSeedArray + iSeedArraySize
     * @param iSecurityLevel - The security level to be used.
     * @param ipVariant - A buffer containing the variant. for c: = ipVariant, size decide by vendor self
     * @param key - A buffer containing the input key.for c: = ioKeyArray + iKeyArraySize
     * @returns A buffer containing the generated key. Return is Buffer, for c: = ioKeyArray, length = oSize
     * @throws Will throw an error if the key generation fails.
     *
     * @example
     * ```typescript
     *
     * const dllPath=path.join(__dirname,'GenerateKeyEx.dll')
     * const sa=new SecureAccessDll(dllPath)
     *
     *const seed=sa.GenerateKeyEx(Buffer.from([1,2,3,4,5]),1,Buffer.from([1,2,3,4,5]),Buffer.from([1,2,3,4,5]))
     * ```
     *
     */
    GenerateKeyEx(ipSeedArray: Buffer, iSecurityLevel: number, ipVariant: Buffer, key: Buffer): Buffer;
    private loadDll;
}

declare const selfDescribe: typeof describe;

declare const testerList: readonly [
                {{#each this.testers}}
                    "{{this}}",
                {{/each}}
            ];
declare const serviceList: readonly [
    {{#each this.services}}
        "{{this}}",
    {{/each}}
];
declare const allServicesSend: readonly [
    {{#each this.services}}
        "{{this}}.send",
    {{/each}}
];
declare const allServicesRecv: readonly [
    {{#each this.services}}
        "{{this}}.recv",
    {{/each}}
];
declare const allSignal: readonly [
    {{#each this.signals}}
        "{{this}}",
    {{/each}}
];
declare const allUdsSeq: readonly [
    {{#each this.udsSeqName}}
        "{{this}}",
    {{/each}}
];
interface Jobs {
{{#each this.jobs}}
    "{{this.name}}": ({{#each this.param}}{{this}},{{/each}}) => DiagRequest[]|Promise<DiagRequest[]>;
{{/each}}
}
/**
 * All services name config in Diagnostic Service.
 * @category UDS
 */
type ServiceName = (typeof serviceList)[number];
/**
 * All testers name config in Diagnostic Service.
 * @category UDS
 */
type TesterName = (typeof testerList)[number];
/**
 * All services name(.send) config in Diagnostic Service.
 * @category UDS
 */
type ServiceNameSend = (typeof allServicesSend)[number];
/**
 * All services name(.recv) config in Diagnostic Service.
 * @category UDS
 */
type ServiceNameRecv = (typeof allServicesRecv)[number];
/**
 * All UDS sequence names configured in Diagnostic Service.
 * @category UDS
 */
type UdsSeqName = (typeof allUdsSeq)[number];
/**
 * All signals name config in Diagnostic Service.
 * @category LIN
 * @category CAN
 */
type SignalName = (typeof allSignal)[number];
/**
 * All variables name config in Diagnostic Service.
 * @category UDS
 */
type VariableMap = {
    "*":number|string|number[],
    {{#each this.variables}}
        "{{this.name}}":{{this.type}},
    {{/each}}
};
/**
 * All jobs name config in Diagnostic Service.
 * @category UDS
 */
type JobName = keyof Jobs;
type EventMapSend = {
    [key in ServiceNameSend]: DiagRequest;
};
type EventMapRecv = {
    [key in ServiceNameRecv]: DiagResponse;
};
type EventMap = EventMapSend & EventMapRecv;
/**
 * @category UDS
 */
type ServiceEvent = {
    send: DiagRequest;
    recv: DiagResponse;
};
/**
 * @category UDS
 */
declare class Service {
    
    private params;
    private isRequest;
    testerName: string;
    constructor(testerName: string, service: ServiceItem, isRequest: boolean);
    valueOf(): string;
    /**
     * Sync service params to tester sequence, after change, the sequence params will be updated.
     *
     * @returns {Promise<void>} A promise that resolves when the event has been emitted.
     *
     * @example
     *
     * ```ts
     * Util.Init(async () => {
     *    const testService0 = DiagRequest.from('Can.testService')
     *    testService.diagSetParameter('key', 0x01)
     *    const testService1 = DiagRequest.from('Can.testService')
     *    console.log(testService0 == testService1) // false
     *    await testService0.syncService()
     *    const testService2 = DiagRequest.from('Can.testService')
     *    console.log(testService0 == testService2) // true
     *
     * })
     * ```
     */
    changeService(): Promise<void>;
    /**
     * Subscribe to an event. When the event occurs, the listener function will be invoked.
     *
     * The valid event name should be:
     * - `'send'`: will be happen before the msg is send
     * - `'recv'`: will be happen when the response msg is recv
     *
     * @param event The event to be listened.
     * @param listener the function when event
     *
     * @example
     *
     * ```ts
     * Util.Init(()=>{
     *     const testService = DiagRequest.from('Can.testService')
     *     testService.On('send', ()=>{
     *         console.log('send event happened.')
     *     })
     *
     *     testService.On('recv', ()=>{
     *         console.log('recv event happened.')
     *     })
     * })
     * ```
     */
    On<T extends keyof ServiceEvent>(event: T, listener: (data: ServiceEvent[T]) => void | Promise<void>): void;
    /**
     * Subscribe to an event, only once.
     *
     * @param event - The event type.
     * @param listener - The function to subscribe.
     */
    Once<T extends keyof ServiceEvent>(event: T, listener: (data: ServiceEvent[T]) => void | Promise<void>): void;
    /**
     * Unsubscribe from an event.
     *
     * @param event - The event type.
     * @param listener - The function to unsubscribe.
     *
     * @example
     *
     * ```ts
     * Util.Init(() => {
     *     const testService = DiagRequest.from('Can.testService');
     *     testService.On('send', () => {
     *         console.log('send event happened.');
     *     });
     
     *     // The following code will not work
     *     testService.Off('send', () => {
     *         console.log('send event happened.');
     *     });
     * });
     * ```
     *
     * > **Note**: To unsubscribe from an event, you must provide a non-anonymous function.
     */
    Off<T extends keyof ServiceEvent>(event: T, listener: (data: ServiceEvent[T]) => void | Promise<void>): void;
    private asyncEmit;
    /**
     * This function will return the service name
     *
     * @example
     *
     * ```ts
     * Util.Init(()=>{
     *     const testService = DiagRequest.from('Can.testService');
     *     console.log('ServiceName:', testService.getServiceName())
     * })
     * ```
     */
    getServiceName(): string;
    /**
     * This function will return the service describe setting in Service.
     * @returns service describe.
     *
     * @example
     *
     * ```ts
     * Util.Init(()=>{
     *     const testService = DiagRequest.from('Can.testService');
     *     console.log('Desc:', testService.getServiceDesc())
     * })
     * ```
     */
    getServiceDesc(): string | undefined;
    /**
     * This function will return the value of the provided parameter.
     * @param paramName param name
     * @returns param value
     *
     * @example
     *
     * ```ts
     * Util.Init(()=>{
     *     const testService = DiagRequest.from('Can.testService');
     *     console.log('SERVICE-ID Buffer:', testService.diagGetParameter('SERVICE-ID'))
     * })
     * ```
     */
    diagGetParameter(paramName: string): string | number;
    /**
     * This function will return the `Buffer` of the provided parameter.
     * @param paramName param name
     * @returns `Buffer` value of provided parameter.
     *
     * @example
     *
     * ```ts
     * Util.Init(()=>{
     *     const testService = DiagRequest.from('Can.testService');
     *     console.log('SERVICE-ID:', testService.diagGetParameterRaw('SERVICE-ID'))
     * })
     * ```
     */
    diagGetParameterRaw(paramName: string): Buffer;
    /**
     * This function will return the bit size of the provided parameter.
     * @param paramName param name
     * @returns param bit size
     *
     * @example
     *
     * ```ts
     * Util.Init(()=>{
     *     const testService = DiagRequest.from('Can.testService');
     *     console.log('SERVICE-ID Size:', testService.diagGetParameterSize('SERVICE-ID'))
     * })
     */
    diagGetParameterSize(paramName: string): number;
    /**
     * This function returns the names of all parameters associated with the given diag.
     *
     * @returns {string[]} An array of strings storing the names of all parameters.
     *
     * @example
     *
     * Util.Init(()=>{
     *     const testService = DiagRequest.from('Can.testService');
     *     console.log('parameter names:', testService.diagGetParameterNames())
     * })
     */
    diagGetParameterNames(): string[];
    /**
     * This function will change the parameter's bit size.
     * @param paramName parameter name
     * @param bitLen new bit size of the provided parameter.
     *
     * @example
     *
     * > It is only advisable to specify the size of num and array parameters.
     *
     * ```ts
     * Util.Init(()=>{
     *     const testService = DiagRequest.from('Can.testService')
     *
     *     // array parameter
     *     console.log('arrayParam bit size:', testService.diagGetParameterSize('arrayParam'))
     *     testService.diagSetParameterSize('arrayParam', 64)
     *     console.log('arrayParam bit size:', testService.diagGetParameterSize('arrayParam'))
     *
     *     // num parameter
     *     console.log('numParam bit size:', testService.diagGetParameterSize('numParam'))
     *     testService.diagSetParameterSize('numParam', 16)
     *     console.log('numParam bit size:', testService.diagGetParameterSize('numParam'))
     *
     *     console.log('ascii bit size:', testService.diagGetParameterSize('asciiParam'))
     *     testService.diagSetParameterSize('asciiParam', 16)
     *     console.log('ascii bit size:', testService.diagGetParameterSize('asciiParam'))
     * })
     * ```
     *
     */
    diagSetParameterSize(paramName: string, bitLen: number): void;
    /**
     * This function will change the provided parameter's value.
     * @param paramName parameter's name need to be changed.
     * @param value new value of the provided parameter.
     *
     * @example
     *
     * > Add relative param in Service.
     *
     * 1. **array parameter**
     *
     *     ```ts
     *     Util.Init(()=>{
     *         // add param arrayParam in Service.
     *         const testService = DiagRequest.from('Can.testService')
     *
     *         console.log('arrayParam:', testService.diagGetParameter('arrayParam'))
     *         testService.diagSetParameter('arrayParam', '12 34 56 78')
     *         console.log('arrayParam:', testService.diagGetParameter('arrayParam'))
     *     })
     *     ```
     *
     * 2. **num parameter**
     *
     *     ```ts
     *     Util.Init(()=>{
     *         // add param arrayParam in Service.
     *         const testService = DiagRequest.from('Can.testService')
     *
     *         // 8 bit number
     *         console.log('8 bits num:', testService.diagGetParameter('numParam'))
     *         testService.diagSetParameter('numParam', '12')
     *         console.log('set parameter with str:', testService.diagGetParameter('numParam'))
     *         testService.diagSetParameter('numParam', 99)
     *         console.log('set parameter with number:', testService.diagGetParameter('numParam'))
     *
     *         // 16 bit number
     *         console.log('8 bits num:', testService.diagGetParameterRaw('numParam'))
     *         testService.diagSetParameterSize('numParam', 16)
     *         console.log('change size to 16 bits:', testService.diagGetParameterRaw('numParam'))
     *         testService.diagSetParameter('numParam', '257')
     *         console.log('set parameter with str', testService.diagGetParameterRaw('numParam'))
     *         testService.diagSetParameter('numParam', 65534)
     *         console.log('set parameter with number', testService.diagGetParameterRaw('numParam'))
     *     })
     *     ```
     * 3. **ascii parameter**
     *
     *     > The ascii parameter formats the input value into a string. It is advisable to avoid using numbers as input.
     *
     *     ```ts
     *     Util.Init(()=>{
     *         // add param arrayParam in Service.
     *         const testService = DiagRequest.from('Can.testService')
     *
     *         // 8 bit number
     *         console.log('8 bits num:', testService.diagGetParameterRaw('asciiParam'))
     *         testService.diagSetParameter('asciiParam', 'A')
     *         console.log('set parameter with str:', testService.diagGetParameterRaw('asciiParam'))
     *
     *         // 16 bit number
     *         console.log('8 bits num:', testService.diagGetParameterRaw('asciiParam'))
     *         await testService.diagSetParameterSize('asciiParam', 16)
     *         console.log('change size to 16 bits:', testService.diagGetParameterRaw('asciiParam'))
     *         await testService.diagSetParameter('asciiParam', 'AB')
     *         console.log('set parameter with str', testService.diagGetParameterRaw('asciiParam'))
     *     })
     *     ```
     * 4. **unicode parameter**
     *
     *     ```ts
     *     Util.Init(()=>{
     *         // add param arrayParam in Service.
     *         const testService = DiagRequest.from('Can.testService')
     *
     *         // 8 bit number
     *         console.log('24 bits num:', testService.diagGetParameter('unicodeParam'))
     *         testService.diagSetParameter('unicodeParam', '❤')
     *         console.log('set parameter with str:', testService.diagGetParameter('unicodeParam'))
     *
     *         // 16 bit number
     *         console.log('48 bits num:', testService.diagGetParameter('unicodeParam'))
     *         testService.diagSetParameterSize('unicodeParam', 48)
     *         console.log('change size to 16 bits:', testService.diagGetParameter('unicodeParam'))
     *         testService.diagSetParameter('unicodeParam', '❤️')
     *         console.log('set parameter with str', testService.diagGetParameter('unicodeParam'))
     *     })
     *     ```
     *
     * 5. **float parameter**
     *
     *     ```ts
     *     Util.Init(()=>{
     *         // add param arrayParam in Service.
     *         const testService = DiagRequest.from('Can.testService')
     *
     *         // 8 bit number
     *         console.log('32 bits num:', testService.diagGetParameter('floatParam'))
     *         testService.diagSetParameter('floatParam', 0.12345)
     *         console.log('set parameter with float:', testService.diagGetParameter('floatParam'))
     *     })
     *     ```
     */
    diagSetParameter(paramName: string, value: string | number): void;
    /**
     * This function will change the provided parameter's value with provided `Buffer` value.
     *
     * @param paramName parameter's name need to be changed.
     * @param {Buffer} value new `Buffer` value of the provided parameter.
     *
     * @example
     *
     * > Add relative param in Service.
     *
     * This function modifies the value of a parameter using a Buffer. The Buffer's value will be transferred at the TP layer.
     * You can generate a Buffer using the following methods:
     *
     * ```ts
     * const newValue1 = Buffer.from([0x12, 0x34, 0x56, 0x78]);
     *
     * const newValue2 = Buffer.alloc(4);
     * newValue2.writeUInt8(0x01, 0);
     * newValue2.writeUInt8(0x02, 1);
     * newValue2.writeUInt8(0x03, 2);
     * newValue2.writeUInt8(0x04, 3);
     *
     * const newValue3 = Buffer.from('11223344', 'hex');
     * ```
     *
     * To modify an array parameter, you can use the following example:
     *
     * ```ts
     * Util.Init(() => {
    
     *     const testService = DiagRequest.from('Can.testService');
     *
     *     console.log('arrayParam:', testService.diagGetParameter('arrayParam'));
     *     const newValue1 = Buffer.from([0x12, 0x34, 0x56, 0x78]);
     *
     *     testService.diagSetParameterRaw('arrayParam', newValue1);
     *     console.log('arrayParam:', testService.diagGetParameter('arrayParam'));
     * });
     * ```
     *
     * > For more examples on changing different parameter types, please refer to the {@link diagSetParameter | `diagSetParameter`} function.
     *
     */
    diagSetParameterRaw(paramName: string, value: Buffer): void;
    /**
     * Sends a diagnostic output command to the specified device.
     *
     * @param deviceName - The name of the device to send the diagnostic command to.
     * @param addressName - The address name associated with the device.
     * @returns The diagnostic output timestamp.
     */
    outputDiag(deviceName?: string, addressName?: string): Promise<number>;
    /**
     * This function modifies all values of a service.
     *
     * @param data - The new data's buffer value.
     *
     * @example
     *
     * This function is typically used by a job to modify all data of a service. The following code demonstrates how to generate a new service and set its raw data:
     *
     * ```ts
     * Util.Register('Can.testJob', async (v) => {
     *     //create a new DiagRequest in Can tester
     *     const testService = new DiagRequest('Can');
     *     const newData = Buffer.from([0x10, 0x01, 0x00, 0x01, 0x02]);
     *     await testService.diagSetRaw(newData);
     *     return [testService];
     * });
     * ```
     *
     * > - Ensure that the job `Can.testJob` is already configured in Service.
     * > - The return type of a job should be a array.
     *
     * You can also modify the raw data of an existing service with the following code:
     *
     * ```ts
     * Util.Init(() => {
    
     *     const testService = DiagRequest.from('Can.testService');
     *     const newData = Buffer.from([0x10, 0x02]);
     *     await testService.diagSetRaw(newData);
     * });
     * ```
     *
     *
     * > - Ensure that the service `Can.testService` is already configured in Service.
     * > - The new raw data size should be equal to the old raw data size.
     */
    diagSetRaw(data: Buffer): void;
    /**
     * This function will return a raw data of one service.
     * @returns raw data of one service.
     *
     * @example
     *
     * ```ts
     * Util.Init(()=>{
    
     *     const testService = DiagRequest.from('Can.testService')
     *     console.log('get raw data:', testService.diagGetRaw())
     * })
     * ```
     */
    diagGetRaw(): Buffer;
}
/**
 * @category UDS
 */
declare class DiagJob extends Service {
    constructor(testerName: string, service: ServiceItem);
    from(jobName: keyof Jobs): DiagJob;
}
/**
 * @category UDS
 */
declare class DiagResponse extends Service {
    constructor(testerName: string, service: ServiceItem);
    /**
     * @param {string} serviceName
     *
     * > serviceName's type '{{{serviceName}}}' is the string configured by Service.
     *
     * @example
     *
     *     ```ts
     *     Util.Init(async ()=>{
     *         // add param arrayParam in Service.
     *         const testService = DiagRequest.from('Can.testService')
     *         testService.On('send', ()=>{
     *             console.log('send event happened.')
     *         })
     *     }
     *     ```
     */
    static from(serviceName: ServiceName): DiagResponse;
    /**
     * @param {DiagRequest} req
     * @returns {DiagResponse}
     *
     * > req's type '{{{DiagRequest}}}' is the DiagRequest object.
     *
     * @example
     *
     * ```ts
     * Util.On('Can.testService.send', (v)=>{
     *     const response = DiagResponse.fromDiagRequest(v)
     * })
     * ```
     */
    static fromDiagRequest(req: DiagRequest): DiagResponse;
    /**
     * This function will return whether the response is a positive response or not.
     * @returns bool
     *
     * @example
     *
     * ```ts
     * Util.On('Can.testService.recv', (v)=>{
     *     console.log('response is positive:', v.diagIsPositiveResponse())
     * })
     * ```
     *
     */
    diagIsPositiveResponse(): boolean;
    /**
     * This function will return the response code of one response.
     *
     * > **NOTE**: Positive response does not have response code.
     *
     * @returns response code.
     *
     * @example
     *
     * // here testService2 is a RequestDownload(0x34) service
     * Util.On('Can.testService2.recv', (v)=>{
     *     console.log('response code', v.diagGetResponseCode())
     * })
     *
     */
    diagGetResponseCode(): number;
}
/**
 * @category UDS
 */
declare class DiagRequest extends Service {
    constructor(testerName: string, service: ServiceItem);
    /**
     * @param {string} serviceName
     *
     * > serviceName's type '{{{serviceName}}}' is the string configured by Service.
     *
     * @example
     *
     *     ```ts
     *     Util.Init(async ()=>{
     *         // add param arrayParam in Service.
     *         const testService = DiagRequest.from('Can.testService')
     *         testService.On('send', ()=>{
     *             console.log('send event happened.')
     *         })
     *     }
     *     ```
     */
    static from(serviceName: ServiceName): DiagRequest;
}
/**
 * @category Util
 */
declare class UtilClass {
    private isMain;
    private event;
    private funcMap;
    private testerName?;
    /**
     * Register a handler function for a job.
     * @param jobs
     * Job name, valid format is \<tester name\>.\<job name\>
     * @param func
     * Handler function for the job
     *
     * @example
     *
     * ```ts
     * Util.Register('Can.testJob', async (v) => {
    
     *     const testService = new DiagRequest();
     *     const newData = Buffer.from([0x10, 0x01, 0x00, 0x01, 0x02]);
     *     await testService.diagSetRaw(newData);
     *     return [testService];
     * });
     * ```
     */
    Register(jobs: JobName, func: Jobs[keyof Jobs]): void;
    private workerOn;
    /**
     * Registers an event listener for CAN messages.
     *
     * @param id - The CAN message ID to listen for. If `true`, listens for all CAN messages.
     * @param fc - The callback function to be invoked when a CAN message is received.
     */
    OnCan(id: number | true, fc: (msg: CanMessage) => void | Promise<void>): void;
    /**
     * Get the tester name, valid in Tester script
     * @returns {string}
     */
    getTesterName(): string | undefined;
    /**
     * Registers an event listener for CAN messages that will be invoked once.
     *
     * @param id - The CAN message ID to listen for. If `true`, listens for all CAN messages.
     * @param fc - The callback function to be invoked when a CAN message is received.
     */
    OnCanOnce(id: number | true, fc: (msg: CanMessage) => void | Promise<void>): void;
    /**
     * Unsubscribes from CAN messages.
     *
     * @param id - The identifier of the CAN message to unsubscribe from. If `true`, unsubscribes from all CAN messages.
     * @param fc - The callback function to remove from the event listeners.
     */
    OffCan(id: number | true, fc: (msg: CanMessage) => void | Promise<void>): void;
    /**
     * Registers an event listener for LIN messages that will be invoked once.
     *
     * @param id - The LIN message ID or ${databaseName}.${frameName} to listen for. If `true`, listens for all LIN messages.
     * @param fc - The callback function to be invoked when a LIN message is received.
     */
    OnLinOnce(id: number | string | true, fc: (msg: LinMsg) => void | Promise<void>): void;
    /**
     * Registers an event listener for LIN messages.
     *
     * @param id - The LIN message ID or ${databaseName}.${frameName} to listen for. If `true`, listens for all LIN messages.
     * @param fc - The callback function to be invoked when a LIN message is received.
     */
    OnLin(id: number | string | true, fc: (msg: LinMsg) => void | Promise<void>): void;
    /**
     * Unsubscribes from LIN messages.
     *
     * @param id - The identifier of the LIN message to unsubscribe from. If `true`, unsubscribes from all LIN messages.
     * @param fc - The callback function to remove from the event listeners.
     */
    OffLin(id: number | string | true, fc: (msg: LinMsg) => void | Promise<void>): void;
    /**
     * Registers an event listener for a specific key.
     *
     * @param key - The key to listen for. Only the first character of the key is used, * is a wildcard.
     * @param fc - The callback function to be executed when the event is triggered.
     *             This can be a synchronous function or a function returning a Promise.
     */
    OnKey(key: string, fc: (key: string) => void | Promise<void>): void;
    /**
     * Registers an event listener for a specific key that will be invoked once.
     *
     * @param key - The key to listen for. Only the first character of the key is used, * is a wildcard.
     * @param fc - The callback function to be executed when the event is triggered.
     *             This can be a synchronous function or a function returning a Promise.
     */
    OnKeyOnce(key: string, fc: (key: string) => void | Promise<void>): void;
    /**
     * Unsubscribes from an event listener for a specific key.
     *
     * @param key - The key to unsubscribe from. Only the first character of the key is used, * is a wildcard.
     * @param fc - The callback function to remove from the event listeners.
     */
    OffKey(key: string, fc: (key: string) => void | Promise<void>): void;
    /**
     * Registers an event listener for a variable update.
     *
     * @param name - The name of the variable to listen for, * is a wildcard.
     * @param fc - The callback function to be executed when the variable is updated.
     *             This can be a synchronous function or a function returning a Promise.
     *             The callback receives an object with name and value properties.
     *
     * @example
     * ```ts
     * // Listen for updates to a specific variable
     * Util.OnVar('temperature', ({name, value}) => {
     *   console.log(`${name} changed to ${value}`);
     * });
     *
     * // Listen for all variable updates using wildcard
     * Util.OnVar('*', ({name, value}) => {
     *   console.log(`Variable ${name} updated to ${value}`);
     * });
     * ```
     */
    OnVar<Name extends keyof VariableMap>(name: Name, fc: ({ name, value }: {
        name: Name;
        value: VariableMap[Name];
    }) => void | Promise<void>): void;
    /**
     * Registers an event listener for a variable update that will be invoked once.
     *
     * @param name - The name of the variable to listen for, * is a wildcard.
     * @param fc - The callback function to be executed when the variable is updated.
     *             This can be a synchronous function or a function returning a Promise.
     *             The callback receives an object with name and value properties.
     */
    OnVarOnce<Name extends keyof VariableMap>(name: Name, fc: ({ name, value }: {
        name: Name;
        value: VariableMap[Name];
    }) => void | Promise<void>): void;
    /**
     * Unsubscribes from an event listener for a variable update.
     *
     * @param name - The name of the variable to unsubscribe from, * is a wildcard.
     * @param fc - The callback function to remove from the event listeners.
     */
    OffVar<Name extends keyof VariableMap>(name: Name, fc: ({ name, value }: {
        name: Name;
        value: VariableMap[Name];
    }) => void | Promise<void>): void;
    /**
     * Subscribe to an event once, invoking the registered function when the event is emitted.
     * @param eventName
     * Service name, formatted as \<tester name\>.\<service name\>.\<send|recv\>
     *
     * @param listener
     * Function to be called when the event is emitted
     *
     * @example
     *
     * ```ts
     * Util.OnOnce('Can.testService.send', async (req) => {
     *    // The req is a `DiagRequest`
     *    console.log(req.getServiceName(), ': send once');
     * });
     * ```
     */
    OnOnce<Name extends keyof EventMap>(eventName: Name, listener: (eventData: EventMap[Name]) => void | Promise<void>): void;
    /**
     * Subscribe to an event, invoking the registered function when the event is emitted.
     * @param eventName
     * Service name, formatted as \<tester name\>.\<service name\>.\<send|recv\>
     *
     * @param listener
     * Function to be called when the event is emitted
     *
     * @example
     *
     * > The `UDS` is a UDSClass type and has already been created by Service.
     *
     * 1. *send functions*
     *
     *     ```ts
     *     Util.On('Can.testService.send', async (req) => {
     *        // The req is a `DiagRequest`
     *        console.log(req.getServiceName(), ': send');
     *     });
     *     ```
     * 2. *recv function*
     *
     *     ```ts
     *     Util.On('Can.testService.recv', async (req) => {
     *        // The req is a `DiagResponse`
     *        console.log(req.getServiceName(), ':recv');
     *     });
     *     ```
     *
     */
    On<Name extends keyof EventMap>(eventName: Name, listener: (eventData: EventMap[Name]) => void | Promise<void>): void;
    /**
     * Unsubscribe from an event.
     *
     * > Only non-anonymous functions can be unsubscribed.
     *
     * @param eventName
     * Service name, formatted as \<tester name\>.\<service name\>.\<send|recv\>
     *
     * @param listener
     * Function to be unsubscribed
     *
     * @example
     *
     * ```ts
     * Util.On('Can.testService.send', ()=>{
     *     console.log('this function will not be Off')
     * })
     *
     * Util.Off('Can.testService.send', ()=>{
     *     console.log('this function will not be Off')
     * })
     *
     * ```
     *
     */
    Off<Name extends keyof EventMap>(eventName: Name, listener: (eventData: EventMap[Name]) => void | Promise<void>): void;
    private start;
    private canMsg;
    private linMsg;
    private keyDown;
    private varUpdate;
    private evnetDone;
    constructor();
    /**
     * Register a function, this function will be invoked when UDSClass is initialized.
     * @param fc Non-async or async function
     *
     * @example
     *
     * - Perform actions following UDS initialization using a normal function.
     *     ```ts
     *     Util.Init(()=>{
     *       console.log('Hello UDS!')
     *     })
     *     ```
     *
     * - Perform actions following UDS initialization using an async function.
     *     ```ts
     *     Util.Init(async ()=>{
     *       const file=await fs.readFile(path.join(process.env.PROJECT_ROOT,'file.bin'))
     *       let length=file.length
     *       console.log('Hello UDS file! file length is', length)
     *     })
     *     ```
     *
     * - The last registered function will override the previous ones.
     *     ```ts
     *     // The following code will be ignored
     *     Util.Init(async ()=>{
     *         console.log('1')
     *     })
     *
     *     // The following code will take effect
     *     Util.Init(async ()=>{
     *         console.log('2')
     *     })
     *     ```
     */
    Init(fc: () => void | Promise<void>): void;
    /**
     * Register a function, this function will be invoked when UDSClass is terminated.
     * @param fc Non-async or async function
     *
     * @example
     *
     *
     */
    End(fc: () => void | Promise<void>): void;
}
/**
 * Global instance of UDSClass, providing access to UDS functionality throughout the application.
 * Use this instance to interact with UDS features and services.
 *
 * @category Util
 * @type {UDSClass}
 *
 * @example
 * 1. *Init function*
 *     - Perform actions following UDS initialization using a normal function.
 *         ```ts
 *         Util.Init(()=>{
 *           console.log('Hello UDS!')
 *         })
 *         ```
 *
 *     - Perform actions following UDS initialization using an async function.
 *         ```ts
 *         Util.Init(async ()=>{
 *           const file=await fs.readFile(path.join(process.env.PROJECT_ROOT,'file.bin'))
 *           let length=file.length
 *           console.log('Hello UDS file! file length is', length)
 *         })
 *         ```
 *
 * 2. *send functions*
 *     > * This function will be called after the message has been sent.
 *     > * Please replace `Can.DiagRequest.send` with your own service item name. The format is `<tester name>.<service item name>.send`
 *
 *     ```ts
 *     Util.On('Can.DiagRequest.send', async (req) => {
 *        // The req is a `DiagRequest`
 *        console.log(req.getServiceName(), ': send');
 *     });
 *     ```
 *
 * 3. *recv function*
 *     > * This function will be called after the response message has been received.
 *     > * Please replace `Can.DiagRequest.recv` with your own service item name. The format is `<tester name>.<service item name>.recv`
 *
 *     ```ts
 *     Util.On('Can.DiagRequest.recv', async (req) => {
 *        // The req is a `DiagResponse`
 *        console.log(req.getServiceName(), ':recv');
 *     });
 *     ```
 *
 * 4. **More**
 *     > For more details, please refer to the {@link UtilClass | `UtilClass`} class.
 */
declare const Util: UtilClass;
/**
 * Sends a CAN message
 *
 * @category CAN
 * @param {CanMessage} msg - The CAN message to be sent
 * @returns {Promise<number>} - Returns a promise that resolves to sent timestamp
 */
declare function output(msg: CanMessage): Promise<number>;
/**
 * Sends a LIN message
 *
 * @category LIN
 * @param {LinMsg} msg - The LIN message to be sent
 * @returns {Promise<number>} - Returns a promise that resolves to sent timestamp
 */
declare function output(msg: LinMsg): Promise<number>;
/**
 * Set a signal value
 *
 * @category LIN
 * @category CAN
 * @param {SignalName} signal - The signal to set, dbName.SignalName
 * @param {number|number[]} value - The value to set, can be single number or array
 * @returns {Promise<void>} - Returns a promise that resolves when value is set
 *
 * @example
 * ```ts
 * // Set single value signal
 * await setSignal(lin.xxxx', 123);
 *
 * // Set array value signal
 * await setSignal('lin.xxxx', [1, 2, 3, 4]);
 * ```
 */
declare function setSignal(signal: SignalName, value: number | number[] | string): Promise<void>;
/**
 * Set a variable value
 *
 * @category Variable
 * @param {keyof VariableMap} name - The variable name
 * @param {number|number[]|string} value - The value to set, can be single number or array
 * @returns {Promise<void>} - Returns a promise that resolves when value is set
 *
 * @example
 * ```ts
 * // Set single value signal
 * await setVar('var2', 123);
 *
 * // Set array value signal
 * await setVar('namespace.var1', [1, 2, 3, 4]);
 * ```
 */
declare function setVar<T extends keyof VariableMap>(name: T, value: VariableMap[T]): Promise<void>;
/**
 * Run a UDS sequence
 *
 * @category UDS
 * @param {UdsSeqName} seqName - The name of the UDS sequence to run
 * @param {string} [device] - The optional device name to run the sequence on when multiple devices are connected
 * @returns {Promise<void>} - Returns a promise that resolves when sequence completes
 *
 * @example
 * ```ts
 * // Run a UDS sequence
 * await runUdsSeq('MySequence');
 *
 * // Run sequence on specific device
 * await runUdsSeq('MySequence', 'Device1');
 * ```
 */
declare function runUdsSeq(seqName: UdsSeqName, device?: string): Promise<void>;
/**
 * Stop a running UDS sequence
 *
 * @category UDS
 * @param {UdsSeqName} seqName - The name of the UDS sequence to stop
 * @param {string} [device] - The optional device name when multiple devices are connected
 * @returns {Promise<void>} - Returns a promise that resolves when sequence is stopped
 *
 * @example
 * ```ts
 * // Stop a UDS sequence
 * await stopUdsSeq('MySequence');
 * ```
 */
declare function stopUdsSeq(seqName: UdsSeqName, device?: string): Promise<void>;
/**
 * Register a virtual entity
 *
 * @category DOIP
 * @param {EntityAddr} entity - The entity to be registered.
 * @param {string} ip - The IP address of the entity, if node connected to multiple devices.
 * @returns {Promise<void>} - Returns a promise that resolves when the entity is registered.
 */
declare function RegisterEthVirtualEntity(entity: VinInfo, ip?: string): Promise<void>;
type TestEventGenerator = Parameters<typeof dot>[0];
declare function reporter(source: TestEventGenerator): AsyncGenerator<never, void, unknown>;

export { CAN_ADDR_FORMAT, CAN_ADDR_TYPE, CAN_ID_TYPE, type CanMessage, type CanMsgType, DiagJob, DiagRequest, DiagResponse, type EntityAddr, type JobName, LinChecksumType, LinDirection, LinMode, type LinMsg, RegisterEthVirtualEntity, SecureAccessDll, type ServiceEvent, type ServiceId, type ServiceItem, type ServiceName, type ServiceNameRecv, type ServiceNameSend, type SignalName, type TesterName, type UdsSeqName, Util, UtilClass, type VariableMap, selfDescribe as describe, output, reporter, runUdsSeq, setSignal, setVar, stopUdsSeq };
