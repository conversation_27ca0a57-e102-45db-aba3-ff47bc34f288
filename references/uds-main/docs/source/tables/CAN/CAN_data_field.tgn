{"rows_views": [[{"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "center", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "center", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}], [{"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "center", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}], [{"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "center", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}], [{"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "center", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}]], "model": {"rows": [[{"value": "DLC", "cspan": 1, "rspan": 1, "markup": [1, 3]}, {"value": "Description", "cspan": 1, "rspan": 1, "markup": [1, 11]}], [{"value": "<8", "cspan": 1, "rspan": 1, "markup": [1, 2]}, {"value": "*Valid only for CAN frames using data optimization*\n\nValues in this range are only valid for Single Frame, Flow Control and\n\nConsecutive Frame that use CAN frame data optimization.", "cspan": 1, "rspan": 1, "markup": [3, 51, 1, 129]}], [{"value": "8", "cspan": 1, "rspan": 1, "markup": [1, 1]}, {"value": "*Configured CAN frame maximum payload length of 8 bytes*\n\nFor the use with CLASSICAL CAN and CAN FD type frames.", "cspan": 1, "rspan": 1, "markup": [1, 112]}], [{"value": ">8 \n", "cspan": 1, "rspan": 1, "markup": [1, 4]}, {"value": "*Configured CAN frame maximum payload length greater than 8 bytes*\n\nFor the use with CAN FD type frames only.", "cspan": 1, "rspan": 1, "markup": [1, 109]}]]}, "theme": null, "fixed_layout": false, "markup": {"instances": [{}, {"style": {"fontWeight": "", "fontStyle": "", "textDecoration": "", "color": "", "backgroundColor": ""}}, null, {"style": {"fontWeight": "", "fontStyle": "italic", "textDecoration": "", "color": "", "backgroundColor": ""}}]}, "options": {}}