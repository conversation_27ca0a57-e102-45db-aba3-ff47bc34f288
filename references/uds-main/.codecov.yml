codecov:
  archive:
    uploads: false

coverage:
  range: 50...100
  round: down
  precision: 2
  status:
    project:
      default:
        target: 100%
      unit-tests:
        target: 100%
        flags:
          - unit-tests
      unit-tests-branch:
        target: 100%
        flags:
          - unit-tests-branch
      integration-tests:
        target: 80%
        flags:
          - integration-tests
      integration-tests-branch:
        target: 50%
        flags:
          - integration-tests-branch
      performance-tests:
        flags:
          - performance-tests

comment:
  layout: "reach, diff, flags, files"
  behavior: default
  require_changes: false
  show_carryforward_flags: true
