<?xml version="1.0" encoding="utf-8"?>
<root>
  <!--
    Microsoft ResX Schema

    Version 2.0

    The primary goals of this format is to allow a simple XML format
    that is mostly human readable. The generation and parsing of the
    various data types are done through the TypeConverter classes
    associated with the data types.

    Example:

    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>

    There are any number of "resheader" rows that contain simple
    name/value pairs.

    Each data row contains a name, and value. The row also contains a
    type or mimetype. Type corresponds to a .NET class that support
    text/value conversion through the TypeConverter architecture.
    Classes that don't support this are serialized and stored with the
    mimetype set.

    The mimetype is used for serialized objects, and tells the
    ResXResourceReader how to depersist the object. This is currently not
    extensible. For a given mimetype the value must be set accordingly:

    Note - application/x-microsoft.net.object.binary.base64 is the format
    that the ResXResourceWriter will generate, however the reader can
    read any of the formats listed below.

    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="listViewDevices.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="columnHeaderAddress.Text" xml:space="preserve">
    <value>Address</value>
  </data>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="columnHeaderAddress.Width" type="System.Int32, mscorlib">
    <value>193</value>
  </data>
  <data name="columnHeaderName.Text" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="columnHeaderName.Width" type="System.Int32, mscorlib">
    <value>322</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="listViewDevices.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 41</value>
  </data>
  <data name="listViewDevices.Size" type="System.Drawing.Size, System.Drawing">
    <value>599, 254</value>
  </data>
  <data name="listViewDevices.TabIndex" type="System.Int32, mscorlib">
    <value>4</value>
  </data>
  <data name="&gt;&gt;listViewDevices.Name" xml:space="preserve">
    <value>listViewDevices</value>
  </data>
  <data name="&gt;&gt;listViewDevices.Type" xml:space="preserve">
    <value>System.Windows.Forms.ListView, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;listViewDevices.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;listViewDevices.ZOrder" xml:space="preserve">
    <value>15</value>
  </data>
  <data name="buttonSearch.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="buttonSearch.Location" type="System.Drawing.Point, System.Drawing">
    <value>18, 518</value>
  </data>
  <data name="buttonSearch.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 23</value>
  </data>
  <data name="buttonSearch.TabIndex" type="System.Int32, mscorlib">
    <value>11</value>
  </data>
  <data name="buttonSearch.Text" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="&gt;&gt;buttonSearch.Name" xml:space="preserve">
    <value>buttonSearch</value>
  </data>
  <data name="&gt;&gt;buttonSearch.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonSearch.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonSearch.ZOrder" xml:space="preserve">
    <value>14</value>
  </data>
  <data name="buttonClose.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="buttonClose.Location" type="System.Drawing.Point, System.Drawing">
    <value>491, 657</value>
  </data>
  <data name="buttonClose.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 23</value>
  </data>
  <data name="buttonClose.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="buttonClose.Text" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="&gt;&gt;buttonClose.Name" xml:space="preserve">
    <value>buttonClose</value>
  </data>
  <data name="&gt;&gt;buttonClose.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonClose.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonClose.ZOrder" xml:space="preserve">
    <value>13</value>
  </data>
  <data name="labelStatus.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelStatus.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 298</value>
  </data>
  <data name="labelStatus.Size" type="System.Drawing.Size, System.Drawing">
    <value>42, 15</value>
  </data>
  <data name="labelStatus.TabIndex" type="System.Int32, mscorlib">
    <value>5</value>
  </data>
  <data name="labelStatus.Text" xml:space="preserve">
    <value>Status:</value>
  </data>
  <data name="&gt;&gt;labelStatus.Name" xml:space="preserve">
    <value>labelStatus</value>
  </data>
  <data name="&gt;&gt;labelStatus.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelStatus.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelStatus.ZOrder" xml:space="preserve">
    <value>12</value>
  </data>
  <data name="buttonTest.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="buttonTest.Location" type="System.Drawing.Point, System.Drawing">
    <value>347, 518</value>
  </data>
  <data name="buttonTest.Size" type="System.Drawing.Size, System.Drawing">
    <value>258, 23</value>
  </data>
  <data name="buttonTest.TabIndex" type="System.Int32, mscorlib">
    <value>12</value>
  </data>
  <data name="buttonTest.Text" xml:space="preserve">
    <value>Test</value>
  </data>
  <data name="&gt;&gt;buttonTest.Name" xml:space="preserve">
    <value>buttonTest</value>
  </data>
  <data name="&gt;&gt;buttonTest.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonTest.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;buttonTest.ZOrder" xml:space="preserve">
    <value>11</value>
  </data>
  <data name="textBoxBluetoothPin.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="textBoxBluetoothPin.Location" type="System.Drawing.Point, System.Drawing">
    <value>18, 481</value>
  </data>
  <data name="textBoxBluetoothPin.MaxLength" type="System.Int32, mscorlib">
    <value>16</value>
  </data>
  <data name="textBoxBluetoothPin.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 23</value>
  </data>
  <data name="textBoxBluetoothPin.TabIndex" type="System.Int32, mscorlib">
    <value>9</value>
  </data>
  <data name="&gt;&gt;textBoxBluetoothPin.Name" xml:space="preserve">
    <value>textBoxBluetoothPin</value>
  </data>
  <data name="&gt;&gt;textBoxBluetoothPin.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxBluetoothPin.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBoxBluetoothPin.ZOrder" xml:space="preserve">
    <value>10</value>
  </data>
  <data name="labelBluetoothPin.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelBluetoothPin.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelBluetoothPin.Location" type="System.Drawing.Point, System.Drawing">
    <value>18, 463</value>
  </data>
  <data name="labelBluetoothPin.Size" type="System.Drawing.Size, System.Drawing">
    <value>84, 15</value>
  </data>
  <data name="labelBluetoothPin.TabIndex" type="System.Int32, mscorlib">
    <value>7</value>
  </data>
  <data name="labelBluetoothPin.Text" xml:space="preserve">
    <value>Bluetooth PIN:</value>
  </data>
  <data name="&gt;&gt;labelBluetoothPin.Name" xml:space="preserve">
    <value>labelBluetoothPin</value>
  </data>
  <data name="&gt;&gt;labelBluetoothPin.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelBluetoothPin.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelBluetoothPin.ZOrder" xml:space="preserve">
    <value>9</value>
  </data>
  <data name="labelBtDevices.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelBtDevices.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 15</value>
  </data>
  <data name="labelBtDevices.Size" type="System.Drawing.Size, System.Drawing">
    <value>163, 15</value>
  </data>
  <data name="labelBtDevices.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="labelBtDevices.Text" xml:space="preserve">
    <value>Bluetooth/Wi-Fi/USB devices:</value>
  </data>
  <data name="&gt;&gt;labelBtDevices.Name" xml:space="preserve">
    <value>labelBtDevices</value>
  </data>
  <data name="&gt;&gt;labelBtDevices.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelBtDevices.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelBtDevices.ZOrder" xml:space="preserve">
    <value>8</value>
  </data>
  <data name="buttonPatchEdiabas.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 22</value>
  </data>
  <data name="buttonPatchEdiabas.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 23</value>
  </data>
  <data name="buttonPatchEdiabas.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="buttonPatchEdiabas.Text" xml:space="preserve">
    <value>Patch</value>
  </data>
  <data name="&gt;&gt;buttonPatchEdiabas.Name" xml:space="preserve">
    <value>buttonPatchEdiabas</value>
  </data>
  <data name="&gt;&gt;buttonPatchEdiabas.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPatchEdiabas.Parent" xml:space="preserve">
    <value>groupBoxEdiabas</value>
  </data>
  <data name="&gt;&gt;buttonPatchEdiabas.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="openFileDialogConfigFile.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="openFileDialogConfigFile.Filter" xml:space="preserve">
    <value>Api32.dll File|Api32.dll</value>
  </data>
  <data name="openFileDialogConfigFile.Title" xml:space="preserve">
    <value>Select ISTA-D EDIABAS\bin directory</value>
  </data>
  <data name="textBoxWifiPassword.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="textBoxWifiPassword.Location" type="System.Drawing.Point, System.Drawing">
    <value>347, 481</value>
  </data>
  <data name="textBoxWifiPassword.MaxLength" type="System.Int32, mscorlib">
    <value>63</value>
  </data>
  <data name="textBoxWifiPassword.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 23</value>
  </data>
  <data name="textBoxWifiPassword.TabIndex" type="System.Int32, mscorlib">
    <value>10</value>
  </data>
  <data name="&gt;&gt;textBoxWifiPassword.Name" xml:space="preserve">
    <value>textBoxWifiPassword</value>
  </data>
  <data name="&gt;&gt;textBoxWifiPassword.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxWifiPassword.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;textBoxWifiPassword.ZOrder" xml:space="preserve">
    <value>7</value>
  </data>
  <data name="labelWiFiPassword.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="labelWiFiPassword.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelWiFiPassword.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelWiFiPassword.Location" type="System.Drawing.Point, System.Drawing">
    <value>347, 463</value>
  </data>
  <data name="labelWiFiPassword.Size" type="System.Drawing.Size, System.Drawing">
    <value>148, 15</value>
  </data>
  <data name="labelWiFiPassword.TabIndex" type="System.Int32, mscorlib">
    <value>8</value>
  </data>
  <data name="labelWiFiPassword.Text" xml:space="preserve">
    <value>Deep OBD Wi-Fi Password:</value>
  </data>
  <data name="&gt;&gt;labelWiFiPassword.Name" xml:space="preserve">
    <value>labelWiFiPassword</value>
  </data>
  <data name="&gt;&gt;labelWiFiPassword.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelWiFiPassword.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelWiFiPassword.ZOrder" xml:space="preserve">
    <value>6</value>
  </data>
  <data name="buttonRestoreEdiabas.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonRestoreEdiabas.Location" type="System.Drawing.Point, System.Drawing">
    <value>142, 22</value>
  </data>
  <data name="buttonRestoreEdiabas.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 23</value>
  </data>
  <data name="buttonRestoreEdiabas.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="buttonRestoreEdiabas.Text" xml:space="preserve">
    <value>Restore</value>
  </data>
  <data name="&gt;&gt;buttonRestoreEdiabas.Name" xml:space="preserve">
    <value>buttonRestoreEdiabas</value>
  </data>
  <data name="&gt;&gt;buttonRestoreEdiabas.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonRestoreEdiabas.Parent" xml:space="preserve">
    <value>groupBoxEdiabas</value>
  </data>
  <data name="&gt;&gt;buttonRestoreEdiabas.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="groupBoxEdiabas.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left</value>
  </data>
  <data name="groupBoxEdiabas.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 547</value>
  </data>
  <data name="groupBoxEdiabas.Size" type="System.Drawing.Size, System.Drawing">
    <value>270, 52</value>
  </data>
  <data name="groupBoxEdiabas.TabIndex" type="System.Int32, mscorlib">
    <value>13</value>
  </data>
  <data name="groupBoxEdiabas.Text" xml:space="preserve">
    <value>EDIABAS</value>
  </data>
  <data name="&gt;&gt;groupBoxEdiabas.Name" xml:space="preserve">
    <value>groupBoxEdiabas</value>
  </data>
  <data name="&gt;&gt;groupBoxEdiabas.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBoxEdiabas.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBoxEdiabas.ZOrder" xml:space="preserve">
    <value>5</value>
  </data>
  <data name="groupBoxVasPc.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Right</value>
  </data>
  <data name="buttonPatchVasPc.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonPatchVasPc.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 22</value>
  </data>
  <data name="buttonPatchVasPc.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 23</value>
  </data>
  <data name="buttonPatchVasPc.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="buttonPatchVasPc.Text" xml:space="preserve">
    <value>Patch</value>
  </data>
  <data name="&gt;&gt;buttonPatchVasPc.Name" xml:space="preserve">
    <value>buttonPatchVasPc</value>
  </data>
  <data name="&gt;&gt;buttonPatchVasPc.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPatchVasPc.Parent" xml:space="preserve">
    <value>groupBoxVasPc</value>
  </data>
  <data name="&gt;&gt;buttonPatchVasPc.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="buttonRestoreVasPc.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonRestoreVasPc.Location" type="System.Drawing.Point, System.Drawing">
    <value>144, 22</value>
  </data>
  <data name="buttonRestoreVasPc.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 23</value>
  </data>
  <data name="buttonRestoreVasPc.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="buttonRestoreVasPc.Text" xml:space="preserve">
    <value>Restore</value>
  </data>
  <data name="&gt;&gt;buttonRestoreVasPc.Name" xml:space="preserve">
    <value>buttonRestoreVasPc</value>
  </data>
  <data name="&gt;&gt;buttonRestoreVasPc.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonRestoreVasPc.Parent" xml:space="preserve">
    <value>groupBoxVasPc</value>
  </data>
  <data name="&gt;&gt;buttonRestoreVasPc.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="groupBoxVasPc.Location" type="System.Drawing.Point, System.Drawing">
    <value>341, 547</value>
  </data>
  <data name="groupBoxVasPc.Size" type="System.Drawing.Size, System.Drawing">
    <value>270, 52</value>
  </data>
  <data name="groupBoxVasPc.TabIndex" type="System.Int32, mscorlib">
    <value>14</value>
  </data>
  <data name="groupBoxVasPc.Text" xml:space="preserve">
    <value>VAS-PC</value>
  </data>
  <data name="&gt;&gt;groupBoxVasPc.Name" xml:space="preserve">
    <value>groupBoxVasPc</value>
  </data>
  <data name="&gt;&gt;groupBoxVasPc.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBoxVasPc.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBoxVasPc.ZOrder" xml:space="preserve">
    <value>4</value>
  </data>
  <data name="groupBoxIstad.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Bottom, Left, Right</value>
  </data>
  <data name="textBoxIstaLocation.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Left, Right</value>
  </data>
  <data name="textBoxIstaLocation.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 22</value>
  </data>
  <data name="textBoxIstaLocation.Size" type="System.Drawing.Size, System.Drawing">
    <value>461, 23</value>
  </data>
  <data name="textBoxIstaLocation.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;textBoxIstaLocation.Name" xml:space="preserve">
    <value>textBoxIstaLocation</value>
  </data>
  <data name="&gt;&gt;textBoxIstaLocation.Type" xml:space="preserve">
    <value>System.Windows.Forms.TextBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;textBoxIstaLocation.Parent" xml:space="preserve">
    <value>groupBoxIstad</value>
  </data>
  <data name="&gt;&gt;textBoxIstaLocation.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <data name="buttonDirIstad.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonDirIstad.Location" type="System.Drawing.Point, System.Drawing">
    <value>6, 51</value>
  </data>
  <data name="buttonDirIstad.Size" type="System.Drawing.Size, System.Drawing">
    <value>156, 23</value>
  </data>
  <data name="buttonDirIstad.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="buttonDirIstad.Text" xml:space="preserve">
    <value>Select Location</value>
  </data>
  <data name="&gt;&gt;buttonDirIstad.Name" xml:space="preserve">
    <value>buttonDirIstad</value>
  </data>
  <data name="&gt;&gt;buttonDirIstad.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonDirIstad.Parent" xml:space="preserve">
    <value>groupBoxIstad</value>
  </data>
  <data name="&gt;&gt;buttonDirIstad.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="buttonPatchIstad.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="buttonPatchIstad.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonPatchIstad.Location" type="System.Drawing.Point, System.Drawing">
    <value>221, 51</value>
  </data>
  <data name="buttonPatchIstad.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 23</value>
  </data>
  <data name="buttonPatchIstad.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="buttonPatchIstad.Text" xml:space="preserve">
    <value>Patch</value>
  </data>
  <data name="&gt;&gt;buttonPatchIstad.Name" xml:space="preserve">
    <value>buttonPatchIstad</value>
  </data>
  <data name="&gt;&gt;buttonPatchIstad.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonPatchIstad.Parent" xml:space="preserve">
    <value>groupBoxIstad</value>
  </data>
  <data name="&gt;&gt;buttonPatchIstad.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="buttonRestoreIstad.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="buttonRestoreIstad.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="buttonRestoreIstad.Location" type="System.Drawing.Point, System.Drawing">
    <value>347, 51</value>
  </data>
  <data name="buttonRestoreIstad.Size" type="System.Drawing.Size, System.Drawing">
    <value>120, 23</value>
  </data>
  <data name="buttonRestoreIstad.TabIndex" type="System.Int32, mscorlib">
    <value>3</value>
  </data>
  <data name="buttonRestoreIstad.Text" xml:space="preserve">
    <value>Restore</value>
  </data>
  <data name="&gt;&gt;buttonRestoreIstad.Name" xml:space="preserve">
    <value>buttonRestoreIstad</value>
  </data>
  <data name="&gt;&gt;buttonRestoreIstad.Type" xml:space="preserve">
    <value>System.Windows.Forms.Button, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;buttonRestoreIstad.Parent" xml:space="preserve">
    <value>groupBoxIstad</value>
  </data>
  <data name="&gt;&gt;buttonRestoreIstad.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="groupBoxIstad.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 601</value>
  </data>
  <data name="groupBoxIstad.Size" type="System.Drawing.Size, System.Drawing">
    <value>473, 79</value>
  </data>
  <data name="groupBoxIstad.TabIndex" type="System.Int32, mscorlib">
    <value>15</value>
  </data>
  <data name="groupBoxIstad.Text" xml:space="preserve">
    <value>ISTA-D</value>
  </data>
  <data name="&gt;&gt;groupBoxIstad.Name" xml:space="preserve">
    <value>groupBoxIstad</value>
  </data>
  <data name="&gt;&gt;groupBoxIstad.Type" xml:space="preserve">
    <value>System.Windows.Forms.GroupBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;groupBoxIstad.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;groupBoxIstad.ZOrder" xml:space="preserve">
    <value>3</value>
  </data>
  <data name="comboBoxLanguage.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="comboBoxLanguage.Location" type="System.Drawing.Point, System.Drawing">
    <value>437, 12</value>
  </data>
  <data name="comboBoxLanguage.Size" type="System.Drawing.Size, System.Drawing">
    <value>174, 23</value>
  </data>
  <data name="comboBoxLanguage.TabIndex" type="System.Int32, mscorlib">
    <value>1</value>
  </data>
  <data name="&gt;&gt;comboBoxLanguage.Name" xml:space="preserve">
    <value>comboBoxLanguage</value>
  </data>
  <data name="&gt;&gt;comboBoxLanguage.Type" xml:space="preserve">
    <value>System.Windows.Forms.ComboBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;comboBoxLanguage.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;comboBoxLanguage.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <data name="labelLanguage.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Right</value>
  </data>
  <data name="labelLanguage.AutoSize" type="System.Boolean, mscorlib">
    <value>True</value>
  </data>
  <data name="labelLanguage.ImeMode" type="System.Windows.Forms.ImeMode, System.Windows.Forms">
    <value>NoControl</value>
  </data>
  <data name="labelLanguage.Location" type="System.Drawing.Point, System.Drawing">
    <value>330, 15</value>
  </data>
  <data name="labelLanguage.Size" type="System.Drawing.Size, System.Drawing">
    <value>62, 15</value>
  </data>
  <data name="labelLanguage.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="labelLanguage.Text" xml:space="preserve">
    <value>Language:</value>
  </data>
  <data name="&gt;&gt;labelLanguage.Name" xml:space="preserve">
    <value>labelLanguage</value>
  </data>
  <data name="&gt;&gt;labelLanguage.Type" xml:space="preserve">
    <value>System.Windows.Forms.Label, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;labelLanguage.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;labelLanguage.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <data name="richTextBoxStatus.Anchor" type="System.Windows.Forms.AnchorStyles, System.Windows.Forms">
    <value>Top, Bottom, Left, Right</value>
  </data>
  <data name="richTextBoxStatus.Location" type="System.Drawing.Point, System.Drawing">
    <value>12, 316</value>
  </data>
  <data name="richTextBoxStatus.ScrollBars" type="System.Windows.Forms.RichTextBoxScrollBars, System.Windows.Forms">
    <value>Vertical</value>
  </data>
  <data name="richTextBoxStatus.Size" type="System.Drawing.Size, System.Drawing">
    <value>599, 144</value>
  </data>
  <data name="richTextBoxStatus.TabIndex" type="System.Int32, mscorlib">
    <value>6</value>
  </data>
  <data name="richTextBoxStatus.Text" xml:space="preserve">
    <value />
  </data>
  <data name="&gt;&gt;richTextBoxStatus.Name" xml:space="preserve">
    <value>richTextBoxStatus</value>
  </data>
  <data name="&gt;&gt;richTextBoxStatus.Type" xml:space="preserve">
    <value>System.Windows.Forms.RichTextBox, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;richTextBoxStatus.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;richTextBoxStatus.ZOrder" xml:space="preserve">
    <value>0</value>
  </data>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>52</value>
  </metadata>
  <data name="$this.AutoScaleDimensions" type="System.Drawing.SizeF, System.Drawing">
    <value>96, 96</value>
  </data>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>623, 692</value>
  </data>
  <data name="$this.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>639, 695</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>EdiabasLib Config Tool</value>
  </data>
  <data name="&gt;&gt;columnHeaderAddress.Name" xml:space="preserve">
    <value>columnHeaderAddress</value>
  </data>
  <data name="&gt;&gt;columnHeaderAddress.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;columnHeaderName.Name" xml:space="preserve">
    <value>columnHeaderName</value>
  </data>
  <data name="&gt;&gt;columnHeaderName.Type" xml:space="preserve">
    <value>System.Windows.Forms.ColumnHeader, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;openFileDialogConfigFile.Name" xml:space="preserve">
    <value>openFileDialogConfigFile</value>
  </data>
  <data name="&gt;&gt;openFileDialogConfigFile.Type" xml:space="preserve">
    <value>System.Windows.Forms.OpenFileDialog, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>FormMain</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>System.Windows.Forms.Form, System.Windows.Forms, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
</root>