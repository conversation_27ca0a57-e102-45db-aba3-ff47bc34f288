//------------------------------------------------------------------------------
// <auto-generated>
//     Dieser Code wurde von einem Tool generiert.
//     Laufzeitversion:4.0.30319.42000
//
//     Änderungen an dieser Datei können falsches Verhalten verursachen und gehen verloren, wenn
//     der Code erneut generiert wird.
// </auto-generated>
//------------------------------------------------------------------------------

namespace EdiabasLibConfigTool.Resources {
    using System;
    
    
    /// <summary>
    ///   Eine stark typisierte Ressourcenklasse zum Suchen von lokalisierten Zeichenfolgen usw.
    /// </summary>
    // Diese Klasse wurde von der StronglyTypedResourceBuilder automatisch generiert
    // -Klasse über ein Tool wie ResGen oder Visual Studio automatisch generiert.
    // Um einen Member hinzuzufügen oder zu entfernen, bearbeiten Sie die .ResX-Datei und führen dann ResGen
    // mit der /str-Option erneut aus, oder Sie erstellen Ihr VS-Projekt neu.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Strings {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Strings() {
        }
        
        /// <summary>
        ///   Gibt die zwischengespeicherte ResourceManager-Instanz zurück, die von dieser Klasse verwendet wird.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("EdiabasLibConfigTool.Resources.Strings", typeof(Strings).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Überschreibt die CurrentUICulture-Eigenschaft des aktuellen Threads für alle
        ///   Ressourcenzuordnungen, die diese stark typisierte Ressourcenklasse verwenden.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Bluetooth init failed: {0} ähnelt.
        /// </summary>
        internal static string BtInitError {
            get {
                return ResourceManager.GetString("BtInitError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Check Connection ähnelt.
        /// </summary>
        internal static string ButtonTestCheck {
            get {
                return ResourceManager.GetString("ButtonTestCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Adapter Configuration ähnelt.
        /// </summary>
        internal static string ButtonTestConfiguration {
            get {
                return ResourceManager.GetString("ButtonTestConfiguration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Connect ähnelt.
        /// </summary>
        internal static string ButtonTestConnect {
            get {
                return ResourceManager.GetString("ButtonTestConnect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Adapter mode: ähnelt.
        /// </summary>
        internal static string CanMode {
            get {
                return ResourceManager.GetString("CanMode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die K-CAN ähnelt.
        /// </summary>
        internal static string CanMode100 {
            get {
                return ResourceManager.GetString("CanMode100", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die D-CAN ähnelt.
        /// </summary>
        internal static string CanMode500 {
            get {
                return ResourceManager.GetString("CanMode500", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Automatic ähnelt.
        /// </summary>
        internal static string CanModeAuto {
            get {
                return ResourceManager.GetString("CanModeAuto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Changing CAN mode to automatic ähnelt.
        /// </summary>
        internal static string CanModeChangeAuto {
            get {
                return ResourceManager.GetString("CanModeChangeAuto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Changing CAN mode failed ähnelt.
        /// </summary>
        internal static string CanModeChangeFailed {
            get {
                return ResourceManager.GetString("CanModeChangeFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die It&apos;s recommended to use automatic adapter mode. ähnelt.
        /// </summary>
        internal static string CanModeNotAuto {
            get {
                return ResourceManager.GetString("CanModeNotAuto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die K-LINE ähnelt.
        /// </summary>
        internal static string CanModeOff {
            get {
                return ResourceManager.GetString("CanModeOff", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Unknown ähnelt.
        /// </summary>
        internal static string CanModeUnknown {
            get {
                return ResourceManager.GetString("CanModeUnknown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Connected ähnelt.
        /// </summary>
        internal static string Connected {
            get {
                return ResourceManager.GetString("Connected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Connecting ... ähnelt.
        /// </summary>
        internal static string Connecting {
            get {
                return ResourceManager.GetString("Connecting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Connection failed ähnelt.
        /// </summary>
        internal static string ConnectionFailed {
            get {
                return ResourceManager.GetString("ConnectionFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Select a device to perform an operation. ähnelt.
        /// </summary>
        internal static string DevicesFound {
            get {
                return ResourceManager.GetString("DevicesFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die No devices found. ähnelt.
        /// </summary>
        internal static string DevicesNotFound {
            get {
                return ResourceManager.GetString("DevicesNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Disconnected Wi-Fi adapter ähnelt.
        /// </summary>
        internal static string DisconnectedAdapter {
            get {
                return ResourceManager.GetString("DisconnectedAdapter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The adapter is using the ELM327 firmware, only the replacement firmware is supported. ähnelt.
        /// </summary>
        internal static string ElmAdapterConnected {
            get {
                return ResourceManager.GetString("ElmAdapterConnected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Firmware too old, please update firmware with Deep OBD App. ähnelt.
        /// </summary>
        internal static string FirmwareTooOld {
            get {
                return ResourceManager.GetString("FirmwareTooOld", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Firmware: ähnelt.
        /// </summary>
        internal static string FirmwareVersion {
            get {
                return ResourceManager.GetString("FirmwareVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The adapter is using the ELM327 firmware, please flash the replacement firmware with the app first. ähnelt.
        /// </summary>
        internal static string FlashReplacementFirmware {
            get {
                return ResourceManager.GetString("FlashReplacementFirmware", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die HTTP response invalid ähnelt.
        /// </summary>
        internal static string HttpResponseIncorrect {
            get {
                return ResourceManager.GetString("HttpResponseIncorrect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die HTTP response OK ähnelt.
        /// </summary>
        internal static string HttpResponseOk {
            get {
                return ResourceManager.GetString("HttpResponseOk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die It&apos;s recommended pointing the ISTA-D registry key:
        ///&apos;{0}&apos;
        ///to an external EdiabsLib instance at:
        ///&apos;{1}&apos;.
        ///You you want to to this? ähnelt.
        /// </summary>
        internal static string IstaRegExtMessage {
            get {
                return ResourceManager.GetString("IstaRegExtMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Storage location ähnelt.
        /// </summary>
        internal static string IstaRegExtTitle {
            get {
                return ResourceManager.GetString("IstaRegExtTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Deutsch ähnelt.
        /// </summary>
        internal static string LanguageDe {
            get {
                return ResourceManager.GetString("LanguageDe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die English ähnelt.
        /// </summary>
        internal static string LanguageEn {
            get {
                return ResourceManager.GetString("LanguageEn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die French ähnelt.
        /// </summary>
        internal static string LanguageFr {
            get {
                return ResourceManager.GetString("LanguageFr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Pусский ähnelt.
        /// </summary>
        internal static string LanguageRu {
            get {
                return ResourceManager.GetString("LanguageRu", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Delay time: {0} ms ähnelt.
        /// </summary>
        internal static string LatencyTime {
            get {
                return ResourceManager.GetString("LatencyTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die This application requires .NET Framework {0} or higher. ähnelt.
        /// </summary>
        internal static string NetFrameworkMissing {
            get {
                return ResourceManager.GetString("NetFrameworkMissing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die EdiabasLib {0} file is missing. ähnelt.
        /// </summary>
        internal static string PatchApiDllMissing {
            get {
                return ResourceManager.GetString("PatchApiDllMissing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die API version: {0} ähnelt.
        /// </summary>
        internal static string PatchApiVersion {
            get {
                return ResourceManager.GetString("PatchApiVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Warning: Unknown API version. ähnelt.
        /// </summary>
        internal static string PatchApiVersionUnknown {
            get {
                return ResourceManager.GetString("PatchApiVersionUnknown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Backup file {0} is already existing. ähnelt.
        /// </summary>
        internal static string PatchBackupFileExisting {
            get {
                return ResourceManager.GetString("PatchBackupFileExisting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die EdiabasLib.config file is already existing. ähnelt.
        /// </summary>
        internal static string PatchConfigExisting {
            get {
                return ResourceManager.GetString("PatchConfigExisting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die EdiabasLib.config file is missing. ähnelt.
        /// </summary>
        internal static string PatchConfigMissing {
            get {
                return ResourceManager.GetString("PatchConfigMissing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Update config file failed. ähnelt.
        /// </summary>
        internal static string PatchConfigUpdateFailed {
            get {
                return ResourceManager.GetString("PatchConfigUpdateFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Update config file successful. ähnelt.
        /// </summary>
        internal static string PatchConfigUpdateOk {
            get {
                return ResourceManager.GetString("PatchConfigUpdateOk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Copy files failed. ähnelt.
        /// </summary>
        internal static string PatchCopyFailed {
            get {
                return ResourceManager.GetString("PatchCopyFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die No VC runtime files installed, copying files locally. ähnelt.
        /// </summary>
        internal static string PatchCopyRuntime {
            get {
                return ResourceManager.GetString("PatchCopyRuntime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Creating backup file {0}. ähnelt.
        /// </summary>
        internal static string PatchCreateBackupFile {
            get {
                return ResourceManager.GetString("PatchCreateBackupFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Patching EDIABAS at: &apos;{0}&apos; ähnelt.
        /// </summary>
        internal static string PatchDirectory {
            get {
                return ResourceManager.GetString("PatchDirectory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Correcting invalid delay time: {0} ms -&gt; {1} ms ähnelt.
        /// </summary>
        internal static string PatchingLatencyTime {
            get {
                return ResourceManager.GetString("PatchingLatencyTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Changing delay time failed. Please correct it manually. ähnelt.
        /// </summary>
        internal static string PatchingLatencyTimeFailed {
            get {
                return ResourceManager.GetString("PatchingLatencyTimeFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;Ediabas default settings (ediabas.ini)&apos; ähnelt.
        /// </summary>
        internal static string PatchIstadInfoEdiabas {
            get {
                return ResourceManager.GetString("PatchIstadInfoEdiabas", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die &apos;HO-ICOM / ENET local network&apos; ähnelt.
        /// </summary>
        internal static string PatchIstadInfoEnet {
            get {
                return ResourceManager.GetString("PatchIstadInfoEnet", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Hint: In ISTA-D Administration -&gt; VCI Config select as Interface type: ähnelt.
        /// </summary>
        internal static string PatchIstadInfoHint {
            get {
                return ResourceManager.GetString("PatchIstadInfoHint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Unable to load {0}.
        ///Has the archive been extracted correctly? ähnelt.
        /// </summary>
        internal static string PatchLoadApiDllFailed {
            get {
                return ResourceManager.GetString("PatchLoadApiDllFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The {0} backup file is not valid. ähnelt.
        /// </summary>
        internal static string PatchNoValidBackupFile {
            get {
                return ResourceManager.GetString("PatchNoValidBackupFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Ediabas {0} file is missing. ähnelt.
        /// </summary>
        internal static string PatchOriginalApiDllMissing {
            get {
                return ResourceManager.GetString("PatchOriginalApiDllMissing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Patching registry key:
        ///&apos;{0}&apos; ähnelt.
        /// </summary>
        internal static string PatchRegistry {
            get {
                return ResourceManager.GetString("PatchRegistry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Hint: In VAS-PC only &quot;Guided Fault Finding&quot; and &quot;Guided Functions&quot;
        ///are supported with EdiabasLib. ähnelt.
        /// </summary>
        internal static string PatchVaspcInfo {
            get {
                return ResourceManager.GetString("PatchVaspcInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The VC++ runtime has not been found.
        ///Install the VC++ runtime
        ///&apos;{0}&apos;
        ///first. ähnelt.
        /// </summary>
        internal static string PatchVCRuntimeInstalled {
            get {
                return ResourceManager.GetString("PatchVCRuntimeInstalled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Read firmware version failed ähnelt.
        /// </summary>
        internal static string ReadFirmwareVersionFailed {
            get {
                return ResourceManager.GetString("ReadFirmwareVersionFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Read adapter mode failed ähnelt.
        /// </summary>
        internal static string ReadModeFailed {
            get {
                return ResourceManager.GetString("ReadModeFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Removing the registry key
        ///&apos;{0}&apos;
        ///failed. ähnelt.
        /// </summary>
        internal static string RemoveRegKeyFailed {
            get {
                return ResourceManager.GetString("RemoveRegKeyFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Removing registry key:
        ///&apos;{0}&apos;. ähnelt.
        /// </summary>
        internal static string RemovingRegKey {
            get {
                return ResourceManager.GetString("RemovingRegKey", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Resetting USB device ... ähnelt.
        /// </summary>
        internal static string ResettingUsbDevice {
            get {
                return ResourceManager.GetString("ResettingUsbDevice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Resetting USB device failed. Disconnect device for manual reset. ähnelt.
        /// </summary>
        internal static string ResetUsbDeviceFailed {
            get {
                return ResourceManager.GetString("ResetUsbDeviceFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Restoring original {0} file failed. ähnelt.
        /// </summary>
        internal static string RestoreApiDllFailed {
            get {
                return ResourceManager.GetString("RestoreApiDllFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Restored original {0} file. ähnelt.
        /// </summary>
        internal static string RestoredApiDll {
            get {
                return ResourceManager.GetString("RestoredApiDll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Restoring EDIABAS at: &apos;{0}&apos; ähnelt.
        /// </summary>
        internal static string RestoreDirectory {
            get {
                return ResourceManager.GetString("RestoreDirectory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die No backup file {0} existing. ähnelt.
        /// </summary>
        internal static string RestoreNoBackupFile {
            get {
                return ResourceManager.GetString("RestoreNoBackupFile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Searching ... ähnelt.
        /// </summary>
        internal static string Searching {
            get {
                return ResourceManager.GetString("Searching", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Searching failed ähnelt.
        /// </summary>
        internal static string SearchingFailed {
            get {
                return ResourceManager.GetString("SearchingFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Searching failed: {0} ähnelt.
        /// </summary>
        internal static string SearchingFailedMessage {
            get {
                return ResourceManager.GetString("SearchingFailedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Only USB adapters with EdiabasLib firmware are supported. ähnelt.
        /// </summary>
        internal static string SupportedUsbAdapters {
            get {
                return ResourceManager.GetString("SupportedUsbAdapters", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Test OK ähnelt.
        /// </summary>
        internal static string TestOk {
            get {
                return ResourceManager.GetString("TestOk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Error ähnelt.
        /// </summary>
        internal static string TitleError {
            get {
                return ResourceManager.GetString("TitleError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Only USB adapters are displayed, which have VID {0} and PID {1}. ähnelt.
        /// </summary>
        internal static string UsbAdaptersHidden {
            get {
                return ResourceManager.GetString("UsbAdaptersHidden", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Wi-Fi access has been rejected.
        ///Enable location access for this app in the system.
        ///Click here to open the system app: {0} ähnelt.
        /// </summary>
        internal static string WifiAccessRejected {
            get {
                return ResourceManager.GetString("WifiAccessRejected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die No Wi-Fi adapter found. ähnelt.
        /// </summary>
        internal static string WifiAdapterError {
            get {
                return ResourceManager.GetString("WifiAdapterError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die The adapter configuration page will be opened in the web browser.
        ///The default root password is {0}. ähnelt.
        /// </summary>
        internal static string WifiUrlOk {
            get {
                return ResourceManager.GetString("WifiUrlOk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Sucht eine lokalisierte Zeichenfolge, die Write adapter mode failed ähnelt.
        /// </summary>
        internal static string WriteModeFailed {
            get {
                return ResourceManager.GetString("WriteModeFailed", resourceCulture);
            }
        }
    }
}
