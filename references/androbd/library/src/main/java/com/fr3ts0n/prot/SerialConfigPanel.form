<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.2" maxVersion="1.2" type="org.netbeans.modules.form.forminfo.JPanelFormInfo">
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
    <AuxValue name="designerSize" type="java.awt.Dimension" value="-84,-19,0,5,115,114,0,18,106,97,118,97,46,97,119,116,46,68,105,109,101,110,115,105,111,110,65,-114,-39,-41,-84,95,68,20,2,0,2,73,0,6,104,101,105,103,104,116,73,0,5,119,105,100,116,104,120,112,0,0,0,-85,0,0,0,-13"/>
  </AuxValues>

  <Layout class="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout"/>
  <SubComponents>
    <Container class="javax.swing.JPanel" name="panData">
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout" value="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout$BorderConstraintsDescription">
          <BorderConstraints direction="Center"/>
        </Constraint>
      </Constraints>

      <Layout class="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout"/>
      <SubComponents>
        <Component class="javax.swing.JLabel" name="lblPort">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="10" style="0"/>
            </Property>
            <Property name="labelFor" type="java.awt.Component" editor="org.netbeans.modules.form.ComponentChooserEditor">
              <ComponentRef name="cbPort"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Port"/>
          </Properties>
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout" value="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout$GridBagConstraintsDescription">
              <GridBagConstraints gridX="-1" gridY="-1" gridWidth="1" gridHeight="1" fill="2" ipadX="0" ipadY="0" insetsTop="0" insetsLeft="0" insetsBottom="0" insetsRight="5" anchor="10" weightX="0.0" weightY="0.0"/>
            </Constraint>
          </Constraints>
        </Component>
        <Component class="javax.swing.JComboBox" name="cbPort">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="10" style="0"/>
            </Property>
            <Property name="model" type="javax.swing.ComboBoxModel" editor="org.netbeans.modules.form.editors2.ComboBoxModelEditor">
              <StringArray count="0"/>
            </Property>
          </Properties>
          <Events>
            <EventHandler event="itemStateChanged" listener="java.awt.event.ItemListener" parameters="java.awt.event.ItemEvent" handler="cbPortItemStateChanged"/>
          </Events>
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout" value="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout$GridBagConstraintsDescription">
              <GridBagConstraints gridX="-1" gridY="-1" gridWidth="1" gridHeight="1" fill="2" ipadX="0" ipadY="0" insetsTop="0" insetsLeft="0" insetsBottom="0" insetsRight="0" anchor="10" weightX="1.0" weightY="0.0"/>
            </Constraint>
          </Constraints>
        </Component>
        <Component class="javax.swing.JLabel" name="lblBaudrate">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="10" style="0"/>
            </Property>
            <Property name="labelFor" type="java.awt.Component" editor="org.netbeans.modules.form.ComponentChooserEditor">
              <ComponentRef name="cbBaudrate"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Baudrate"/>
          </Properties>
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout" value="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout$GridBagConstraintsDescription">
              <GridBagConstraints gridX="0" gridY="1" gridWidth="1" gridHeight="1" fill="2" ipadX="0" ipadY="0" insetsTop="0" insetsLeft="0" insetsBottom="0" insetsRight="5" anchor="10" weightX="0.0" weightY="0.0"/>
            </Constraint>
          </Constraints>
        </Component>
        <Component class="javax.swing.JComboBox" name="cbBaudrate">
          <Properties>
            <Property name="editable" type="boolean" value="true"/>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="10" style="0"/>
            </Property>
            <Property name="model" type="javax.swing.ComboBoxModel" editor="org.netbeans.modules.form.editors2.ComboBoxModelEditor">
              <StringArray count="10">
                <StringItem index="0" value="300"/>
                <StringItem index="1" value="600"/>
                <StringItem index="2" value="1200"/>
                <StringItem index="3" value="2400"/>
                <StringItem index="4" value="4800"/>
                <StringItem index="5" value="9600"/>
                <StringItem index="6" value="19200"/>
                <StringItem index="7" value="38400"/>
                <StringItem index="8" value="57600"/>
                <StringItem index="9" value="115200"/>
              </StringArray>
            </Property>
            <Property name="selectedIndex" type="int" value="5"/>
          </Properties>
          <Events>
            <EventHandler event="itemStateChanged" listener="java.awt.event.ItemListener" parameters="java.awt.event.ItemEvent" handler="SelectionChanged"/>
          </Events>
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout" value="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout$GridBagConstraintsDescription">
              <GridBagConstraints gridX="1" gridY="1" gridWidth="1" gridHeight="1" fill="2" ipadX="0" ipadY="0" insetsTop="0" insetsLeft="0" insetsBottom="0" insetsRight="0" anchor="10" weightX="1.0" weightY="0.0"/>
            </Constraint>
          </Constraints>
        </Component>
        <Component class="javax.swing.JLabel" name="lblDataBits">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="10" style="0"/>
            </Property>
            <Property name="labelFor" type="java.awt.Component" editor="org.netbeans.modules.form.ComponentChooserEditor">
              <ComponentRef name="cbDataBits"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Data Bits"/>
          </Properties>
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout" value="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout$GridBagConstraintsDescription">
              <GridBagConstraints gridX="0" gridY="3" gridWidth="1" gridHeight="1" fill="2" ipadX="0" ipadY="0" insetsTop="0" insetsLeft="0" insetsBottom="0" insetsRight="5" anchor="10" weightX="0.0" weightY="0.0"/>
            </Constraint>
          </Constraints>
        </Component>
        <Component class="javax.swing.JComboBox" name="cbDataBits">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="10" style="0"/>
            </Property>
            <Property name="model" type="javax.swing.ComboBoxModel" editor="org.netbeans.modules.form.editors2.ComboBoxModelEditor">
              <StringArray count="4">
                <StringItem index="0" value="5"/>
                <StringItem index="1" value="6"/>
                <StringItem index="2" value="7"/>
                <StringItem index="3" value="8"/>
              </StringArray>
            </Property>
            <Property name="selectedIndex" type="int" value="3"/>
          </Properties>
          <Events>
            <EventHandler event="itemStateChanged" listener="java.awt.event.ItemListener" parameters="java.awt.event.ItemEvent" handler="SelectionChanged"/>
          </Events>
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout" value="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout$GridBagConstraintsDescription">
              <GridBagConstraints gridX="1" gridY="3" gridWidth="1" gridHeight="1" fill="2" ipadX="0" ipadY="0" insetsTop="0" insetsLeft="0" insetsBottom="0" insetsRight="0" anchor="10" weightX="1.0" weightY="0.0"/>
            </Constraint>
          </Constraints>
        </Component>
        <Component class="javax.swing.JLabel" name="lblStopBits">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="10" style="0"/>
            </Property>
            <Property name="labelFor" type="java.awt.Component" editor="org.netbeans.modules.form.ComponentChooserEditor">
              <ComponentRef name="cbStopBits"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Stop Bits"/>
          </Properties>
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout" value="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout$GridBagConstraintsDescription">
              <GridBagConstraints gridX="0" gridY="4" gridWidth="1" gridHeight="1" fill="2" ipadX="0" ipadY="0" insetsTop="0" insetsLeft="0" insetsBottom="0" insetsRight="5" anchor="10" weightX="0.0" weightY="0.0"/>
            </Constraint>
          </Constraints>
        </Component>
        <Component class="javax.swing.JComboBox" name="cbStopBits">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="10" style="0"/>
            </Property>
            <Property name="model" type="javax.swing.ComboBoxModel" editor="org.netbeans.modules.form.editors2.ComboBoxModelEditor">
              <StringArray count="3">
                <StringItem index="0" value="1"/>
                <StringItem index="1" value="2"/>
                <StringItem index="2" value="1.5"/>
              </StringArray>
            </Property>
          </Properties>
          <Events>
            <EventHandler event="itemStateChanged" listener="java.awt.event.ItemListener" parameters="java.awt.event.ItemEvent" handler="SelectionChanged"/>
          </Events>
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout" value="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout$GridBagConstraintsDescription">
              <GridBagConstraints gridX="1" gridY="4" gridWidth="1" gridHeight="1" fill="2" ipadX="0" ipadY="0" insetsTop="0" insetsLeft="0" insetsBottom="0" insetsRight="0" anchor="10" weightX="1.0" weightY="0.0"/>
            </Constraint>
          </Constraints>
        </Component>
        <Component class="javax.swing.JLabel" name="lblParity">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="10" style="0"/>
            </Property>
            <Property name="labelFor" type="java.awt.Component" editor="org.netbeans.modules.form.ComponentChooserEditor">
              <ComponentRef name="cbParity"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Parity"/>
          </Properties>
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout" value="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout$GridBagConstraintsDescription">
              <GridBagConstraints gridX="0" gridY="2" gridWidth="1" gridHeight="1" fill="2" ipadX="0" ipadY="0" insetsTop="0" insetsLeft="0" insetsBottom="0" insetsRight="5" anchor="10" weightX="0.0" weightY="0.0"/>
            </Constraint>
          </Constraints>
        </Component>
        <Component class="javax.swing.JComboBox" name="cbParity">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="10" style="0"/>
            </Property>
            <Property name="model" type="javax.swing.ComboBoxModel" editor="org.netbeans.modules.form.editors2.ComboBoxModelEditor">
              <StringArray count="5">
                <StringItem index="0" value="None"/>
                <StringItem index="1" value="Odd"/>
                <StringItem index="2" value="Even"/>
                <StringItem index="3" value="Mark"/>
                <StringItem index="4" value="Space"/>
              </StringArray>
            </Property>
          </Properties>
          <Events>
            <EventHandler event="itemStateChanged" listener="java.awt.event.ItemListener" parameters="java.awt.event.ItemEvent" handler="SelectionChanged"/>
          </Events>
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout" value="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout$GridBagConstraintsDescription">
              <GridBagConstraints gridX="1" gridY="2" gridWidth="1" gridHeight="1" fill="2" ipadX="0" ipadY="0" insetsTop="0" insetsLeft="0" insetsBottom="0" insetsRight="0" anchor="10" weightX="1.0" weightY="0.0"/>
            </Constraint>
          </Constraints>
        </Component>
        <Component class="javax.swing.JLabel" name="lblHsRx">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="10" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Handshake Rx"/>
          </Properties>
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout" value="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout$GridBagConstraintsDescription">
              <GridBagConstraints gridX="0" gridY="5" gridWidth="1" gridHeight="1" fill="2" ipadX="0" ipadY="0" insetsTop="0" insetsLeft="0" insetsBottom="0" insetsRight="5" anchor="10" weightX="0.0" weightY="0.0"/>
            </Constraint>
          </Constraints>
        </Component>
        <Component class="javax.swing.JLabel" name="lblHsTx">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="10" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="Handshake Tx"/>
          </Properties>
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout" value="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout$GridBagConstraintsDescription">
              <GridBagConstraints gridX="0" gridY="6" gridWidth="1" gridHeight="1" fill="0" ipadX="0" ipadY="0" insetsTop="0" insetsLeft="0" insetsBottom="0" insetsRight="5" anchor="10" weightX="0.0" weightY="0.0"/>
            </Constraint>
          </Constraints>
        </Component>
        <Component class="javax.swing.JComboBox" name="cbProtocolRx">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="10" style="0"/>
            </Property>
            <Property name="model" type="javax.swing.ComboBoxModel" editor="org.netbeans.modules.form.editors2.ComboBoxModelEditor">
              <StringArray count="3">
                <StringItem index="0" value="None"/>
                <StringItem index="1" value="RTS/CTS"/>
                <StringItem index="2" value="XON/XOFF"/>
              </StringArray>
            </Property>
          </Properties>
          <Events>
            <EventHandler event="itemStateChanged" listener="java.awt.event.ItemListener" parameters="java.awt.event.ItemEvent" handler="SelectionChanged"/>
          </Events>
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout" value="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout$GridBagConstraintsDescription">
              <GridBagConstraints gridX="1" gridY="5" gridWidth="1" gridHeight="1" fill="2" ipadX="0" ipadY="0" insetsTop="0" insetsLeft="0" insetsBottom="0" insetsRight="0" anchor="10" weightX="0.0" weightY="0.0"/>
            </Constraint>
          </Constraints>
        </Component>
        <Component class="javax.swing.JComboBox" name="cbProtocolTx">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="10" style="0"/>
            </Property>
            <Property name="model" type="javax.swing.ComboBoxModel" editor="org.netbeans.modules.form.editors2.ComboBoxModelEditor">
              <StringArray count="3">
                <StringItem index="0" value="None"/>
                <StringItem index="1" value="RTS/CTS"/>
                <StringItem index="2" value="XON/XOFF"/>
              </StringArray>
            </Property>
          </Properties>
          <Events>
            <EventHandler event="itemStateChanged" listener="java.awt.event.ItemListener" parameters="java.awt.event.ItemEvent" handler="SelectionChanged"/>
          </Events>
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout" value="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout$GridBagConstraintsDescription">
              <GridBagConstraints gridX="1" gridY="6" gridWidth="1" gridHeight="1" fill="2" ipadX="0" ipadY="0" insetsTop="0" insetsLeft="0" insetsBottom="0" insetsRight="0" anchor="10" weightX="0.0" weightY="0.0"/>
            </Constraint>
          </Constraints>
        </Component>
      </SubComponents>
    </Container>
    <Container class="javax.swing.JPanel" name="jPanel1">
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout" value="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout$BorderConstraintsDescription">
          <BorderConstraints direction="South"/>
        </Constraint>
      </Constraints>

      <Layout class="org.netbeans.modules.form.compat2.layouts.DesignGridLayout">
        <Property name="columns" type="int" value="0"/>
        <Property name="horizontalGap" type="int" value="1"/>
        <Property name="rows" type="int" value="1"/>
      </Layout>
      <SubComponents>
        <Component class="javax.swing.JToggleButton" name="btnDSR">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="8" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="DSR"/>
            <Property name="enabled" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JToggleButton" name="btnCTS">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="8" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="CTS"/>
            <Property name="enabled" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JToggleButton" name="btnCD">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="8" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="CD"/>
            <Property name="enabled" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JToggleButton" name="btnDTR">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="8" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="DTR"/>
            <Property name="enabled" type="boolean" value="false"/>
          </Properties>
        </Component>
        <Component class="javax.swing.JToggleButton" name="btnRTS">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="8" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" value="RTS"/>
            <Property name="enabled" type="boolean" value="false"/>
          </Properties>
        </Component>
      </SubComponents>
    </Container>
  </SubComponents>
</Form>
