<?xml version="1.0" encoding="UTF-8" ?>

<Form version="1.2" maxVersion="1.2" type="org.netbeans.modules.form.forminfo.JFrameFormInfo">
  <NonVisualComponents>
    <Component class="javax.swing.JFileChooser" name="fChoose">
      <Properties>
        <Property name="fileFilter" type="javax.swing.filechooser.FileFilter" editor="org.netbeans.modules.form.RADConnectionPropertyEditor">
          <Connection code="new ObdFileFilter()" type="code"/>
        </Property>
        <Property name="fileSelectionMode" type="int" value="2"/>
      </Properties>
    </Component>
    <Component class="com.fr3ts0n.ecu.AboutPanel" name="panAbout">
      <Properties>
        <Property name="icon" type="javax.swing.Icon" editor="org.netbeans.modules.form.editors2.IconEditor">
          <Image iconType="0" name="null"/>
        </Property>
      </Properties>
    </Component>
    <Menu class="javax.swing.JMenuBar" name="mbMain">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Dialog" size="10" style="0"/>
        </Property>
      </Properties>
      <SubComponents>
        <Menu class="javax.swing.JMenu" name="mnuFile">
          <Properties>
            <Property name="mnemonic" type="int" value="70"/>
            <Property name="text" type="java.lang.String" value="File"/>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="10" style="0"/>
            </Property>
          </Properties>
          <SubComponents>
            <MenuItem class="javax.swing.JMenuItem" name="miLoad">
              <Properties>
                <Property name="accelerator" type="javax.swing.KeyStroke" editor="org.netbeans.modules.form.editors.KeyStrokeEditor">
                  <KeyStroke key="F3"/>
                </Property>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Dialog" size="10" style="0"/>
                </Property>
                <Property name="mnemonic" type="int" value="76"/>
                <Property name="text" type="java.lang.String" value="Load measurement"/>
              </Properties>
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="miLoadActionPerformed"/>
              </Events>
            </MenuItem>
            <MenuItem class="javax.swing.JMenuItem" name="miSave">
              <Properties>
                <Property name="accelerator" type="javax.swing.KeyStroke" editor="org.netbeans.modules.form.editors.KeyStrokeEditor">
                  <KeyStroke key="F4"/>
                </Property>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Dialog" size="10" style="0"/>
                </Property>
                <Property name="mnemonic" type="int" value="83"/>
                <Property name="text" type="java.lang.String" value="Save measurement"/>
              </Properties>
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="miSaveActionPerformed"/>
              </Events>
            </MenuItem>
          </SubComponents>
        </Menu>
        <Menu class="javax.swing.JMenu" name="mnuComm">
          <Properties>
            <Property name="mnemonic" type="int" value="67"/>
            <Property name="text" type="java.lang.String" value="Communication"/>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="10" style="0"/>
            </Property>
          </Properties>
          <SubComponents>
            <MenuItem class="javax.swing.JMenuItem" name="miCommConfigure">
              <Properties>
                <Property name="accelerator" type="javax.swing.KeyStroke" editor="org.netbeans.modules.form.editors.KeyStrokeEditor">
                  <KeyStroke key="F8"/>
                </Property>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Dialog" size="10" style="0"/>
                </Property>
                <Property name="mnemonic" type="int" value="67"/>
                <Property name="text" type="java.lang.String" value="Port Configuration..."/>
              </Properties>
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="miCommConfigureActionPerformed"/>
              </Events>
            </MenuItem>
            <MenuItem class="javax.swing.JMenuItem" name="miCommInit">
              <Properties>
                <Property name="accelerator" type="javax.swing.KeyStroke" editor="org.netbeans.modules.form.editors.KeyStrokeEditor">
                  <KeyStroke key="F5"/>
                </Property>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Dialog" size="10" style="0"/>
                </Property>
                <Property name="mnemonic" type="int" value="73"/>
                <Property name="text" type="java.lang.String" value="Initialize"/>
              </Properties>
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="connectEcu"/>
              </Events>
            </MenuItem>
            <MenuItem class="javax.swing.JMenuItem" name="miCommStop">
              <Properties>
                <Property name="accelerator" type="javax.swing.KeyStroke" editor="org.netbeans.modules.form.editors.KeyStrokeEditor">
                  <KeyStroke key="F6"/>
                </Property>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Dialog" size="10" style="0"/>
                </Property>
                <Property name="mnemonic" type="int" value="112"/>
                <Property name="text" type="java.lang.String" value="Stop"/>
              </Properties>
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="miCommStopActionPerformed"/>
              </Events>
            </MenuItem>
          </SubComponents>
        </Menu>
        <Menu class="javax.swing.JMenu" name="mnuHelp">
          <Properties>
            <Property name="mnemonic" type="int" value="72"/>
            <Property name="text" type="java.lang.String" value="Help"/>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="10" style="0"/>
            </Property>
          </Properties>
          <SubComponents>
            <MenuItem class="javax.swing.JMenuItem" name="miAbout">
              <Properties>
                <Property name="accelerator" type="javax.swing.KeyStroke" editor="org.netbeans.modules.form.editors.KeyStrokeEditor">
                  <KeyStroke key="F1"/>
                </Property>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Dialog" size="10" style="0"/>
                </Property>
                <Property name="mnemonic" type="int" value="65"/>
                <Property name="text" type="java.lang.String" value="About"/>
              </Properties>
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="miAboutActionPerformed"/>
              </Events>
            </MenuItem>
          </SubComponents>
        </Menu>
      </SubComponents>
    </Menu>
  </NonVisualComponents>
  <Properties>
    <Property name="defaultCloseOperation" type="int" value="3"/>
    <Property name="title" type="java.lang.String" editor="org.netbeans.modules.form.RADConnectionPropertyEditor">
      <Connection code="product+&quot; &quot;+version" type="code"/>
    </Property>
  </Properties>
  <SyntheticProperties>
    <SyntheticProperty name="menuBar" type="java.lang.String" value="mbMain"/>
    <SyntheticProperty name="formSizePolicy" type="int" value="1"/>
  </SyntheticProperties>
  <AuxValues>
    <AuxValue name="FormSettings_autoResourcing" type="java.lang.Integer" value="0"/>
    <AuxValue name="FormSettings_autoSetComponentName" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_generateFQN" type="java.lang.Boolean" value="true"/>
    <AuxValue name="FormSettings_generateMnemonicsCode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_i18nAutoMode" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_layoutCodeTarget" type="java.lang.Integer" value="2"/>
    <AuxValue name="FormSettings_listenerGenerationStyle" type="java.lang.Integer" value="1"/>
    <AuxValue name="FormSettings_variablesLocal" type="java.lang.Boolean" value="false"/>
    <AuxValue name="FormSettings_variablesModifier" type="java.lang.Integer" value="2"/>
    <AuxValue name="designerSize" type="java.awt.Dimension" value="-84,-19,0,5,115,114,0,18,106,97,118,97,46,97,119,116,46,68,105,109,101,110,115,105,111,110,65,-114,-39,-41,-84,95,68,20,2,0,2,73,0,6,104,101,105,103,104,116,73,0,5,119,105,100,116,104,120,112,0,0,1,-100,0,0,1,-54"/>
  </AuxValues>

  <Layout class="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout"/>
  <SubComponents>
    <Container class="javax.swing.JTabbedPane" name="tabMain">
      <Properties>
        <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
          <Font name="Dialog" size="10" style="0"/>
        </Property>
        <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
          <Dimension value="[520, 350]"/>
        </Property>
      </Properties>
      <Events>
        <EventHandler event="stateChanged" listener="javax.swing.event.ChangeListener" parameters="javax.swing.event.ChangeEvent" handler="tabMainStateChanged"/>
      </Events>
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout" value="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout$BorderConstraintsDescription">
          <BorderConstraints direction="Center"/>
        </Constraint>
      </Constraints>

      <Layout class="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout"/>
      <SubComponents>
        <Container class="javax.swing.JPanel" name="panStart">
          <Properties>
            <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
              <Color blue="ff" green="ff" red="ff" type="rgb"/>
            </Property>
          </Properties>
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout" value="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout$JTabbedPaneConstraintsDescription">
              <JTabbedPaneConstraints tabName="About">
                <Property name="tabTitle" type="java.lang.String" value="About"/>
              </JTabbedPaneConstraints>
            </Constraint>
          </Constraints>

          <Layout class="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout"/>
          <SubComponents>
            <Component class="javax.swing.JLabel" name="lblFooter">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Dialog" size="10" style="0"/>
                </Property>
                <Property name="horizontalAlignment" type="int" value="0"/>
                <Property name="text" type="java.lang.String" editor="org.netbeans.modules.form.RADConnectionPropertyEditor">
                  <Connection code="copyright" type="code"/>
                </Property>
              </Properties>
              <Constraints>
                <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout" value="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout$BorderConstraintsDescription">
                  <BorderConstraints direction="South"/>
                </Constraint>
              </Constraints>
            </Component>
            <Container class="javax.swing.JPanel" name="jPanel1">
              <Properties>
                <Property name="background" type="java.awt.Color" editor="org.netbeans.beaninfo.editors.ColorEditor">
                  <Color blue="ff" green="ff" red="ff" type="rgb"/>
                </Property>
                <Property name="border" type="javax.swing.border.Border" editor="org.netbeans.modules.form.editors2.BorderEditor">
                  <Border info="org.netbeans.modules.form.compat2.border.EmptyBorderInfo">
                    <EmptyBorder bottom="10" left="10" right="10" top="10"/>
                  </Border>
                </Property>
              </Properties>
              <Constraints>
                <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout" value="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout$BorderConstraintsDescription">
                  <BorderConstraints direction="Center"/>
                </Constraint>
              </Constraints>

              <Layout class="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout">
                <Property name="verticalGap" type="int" value="25"/>
              </Layout>
              <SubComponents>
                <Component class="javax.swing.JLabel" name="lblTitle">
                  <Properties>
                    <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                      <Font name="Dialog" size="18" style="1"/>
                    </Property>
                    <Property name="horizontalAlignment" type="int" value="0"/>
                    <Property name="text" type="java.lang.String" editor="org.netbeans.modules.form.RADConnectionPropertyEditor">
                      <Connection code="product" type="code"/>
                    </Property>
                    <Property name="verticalAlignment" type="int" value="1"/>
                  </Properties>
                  <Constraints>
                    <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout" value="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout$BorderConstraintsDescription">
                      <BorderConstraints direction="North"/>
                    </Constraint>
                  </Constraints>
                </Component>
                <Component class="com.fr3ts0n.pvs.gui.PvTable" name="TblVehIDs">
                  <Properties>
                    <Property name="autoResizeMode" type="int" value="5"/>
                    <Property name="focusable" type="boolean" value="false"/>
                    <Property name="name" type="java.lang.String" value="TblVids" noResource="true"/>
                    <Property name="opaque" type="boolean" value="false"/>
                    <Property name="rowSelectionAllowed" type="boolean" value="false"/>
                    <Property name="showGrid" type="boolean" value="false"/>
                  </Properties>
                  <AuxValues>
                    <AuxValue name="JavaCodeGenerator_CreateCodeCustom" type="java.lang.String" value="new com.fr3ts0n.pvs.gui.PvTable(prt.VidPvs)"/>
                  </AuxValues>
                  <Constraints>
                    <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout" value="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout$BorderConstraintsDescription">
                      <BorderConstraints direction="Center"/>
                    </Constraint>
                  </Constraints>
                </Component>
                <Component class="javax.swing.JLabel" name="jLabel1">
                  <Properties>
                    <Property name="horizontalAlignment" type="int" value="0"/>
                    <Property name="icon" type="javax.swing.Icon" editor="org.netbeans.modules.form.editors2.IconEditor">
                      <Image iconType="3" name="/com.fr3ts0n.ecu/res/javag.png"/>
                    </Property>
                  </Properties>
                  <Constraints>
                    <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout" value="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout$BorderConstraintsDescription">
                      <BorderConstraints direction="South"/>
                    </Constraint>
                  </Constraints>
                </Component>
              </SubComponents>
            </Container>
          </SubComponents>
        </Container>
        <Component class="com.fr3ts0n.ecu.ObdDtcPanel" name="panObdDtc">
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout" value="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout$JTabbedPaneConstraintsDescription">
              <JTabbedPaneConstraints tabName="Fault Codes">
                <Property name="tabTitle" type="java.lang.String" value="Fault Codes"/>
              </JTabbedPaneConstraints>
            </Constraint>
          </Constraints>
        </Component>
        <Component class="com.fr3ts0n.ecu.ObdDataPanel" name="panObdData">
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout" value="org.netbeans.modules.form.compat2.layouts.support.JTabbedPaneSupportLayout$JTabbedPaneConstraintsDescription">
              <JTabbedPaneConstraints tabName="Data">
                <Property name="tabTitle" type="java.lang.String" value="Data"/>
              </JTabbedPaneConstraints>
            </Constraint>
          </Constraints>
        </Component>
      </SubComponents>
    </Container>
    <Container class="javax.swing.JPanel" name="panFooter">
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout" value="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout$BorderConstraintsDescription">
          <BorderConstraints direction="South"/>
        </Constraint>
      </Constraints>

      <Layout class="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout"/>
      <SubComponents>
        <Component class="javax.swing.JLabel" name="lblMessage">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="10" style="0"/>
            </Property>
            <Property name="text" type="java.lang.String" editor="org.netbeans.modules.form.RADConnectionPropertyEditor">
              <Connection code="String.format(&quot;%s %s&quot;, product,version)" type="code"/>
            </Property>
            <Property name="border" type="javax.swing.border.Border" editor="org.netbeans.modules.form.editors2.BorderEditor">
              <Border info="org.netbeans.modules.form.compat2.border.BevelBorderInfo">
                <BevelBorder bevelType="1"/>
              </Border>
            </Property>
          </Properties>
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout" value="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout$GridBagConstraintsDescription">
              <GridBagConstraints gridX="-1" gridY="-1" gridWidth="1" gridHeight="1" fill="1" ipadX="0" ipadY="0" insetsTop="0" insetsLeft="0" insetsBottom="0" insetsRight="0" anchor="10" weightX="1.0" weightY="0.0"/>
            </Constraint>
          </Constraints>
        </Component>
        <Component class="javax.swing.JLabel" name="lblStatus">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="10" style="0"/>
            </Property>
            <Property name="horizontalAlignment" type="int" value="0"/>
            <Property name="text" type="java.lang.String" value="Status"/>
            <Property name="border" type="javax.swing.border.Border" editor="org.netbeans.modules.form.editors2.BorderEditor">
              <Border info="org.netbeans.modules.form.compat2.border.CompoundBorderInfo">
                <CompoundBorder>
                  <Border PropertyName="outside" info="org.netbeans.modules.form.compat2.border.BevelBorderInfo">
                    <BevelBorder bevelType="1"/>
                  </Border>
                  <Border PropertyName="inside" info="org.netbeans.modules.form.compat2.border.EmptyBorderInfo">
                    <EmptyBorder bottom="1" left="3" right="3" top="1"/>
                  </Border>
                </CompoundBorder>
              </Border>
            </Property>
            <Property name="minimumSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
              <Dimension value="[90, 18]"/>
            </Property>
            <Property name="opaque" type="boolean" value="true"/>
            <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
              <Dimension value="[90, 18]"/>
            </Property>
          </Properties>
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout" value="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout$GridBagConstraintsDescription">
              <GridBagConstraints gridX="-1" gridY="-1" gridWidth="1" gridHeight="1" fill="1" ipadX="0" ipadY="0" insetsTop="0" insetsLeft="0" insetsBottom="0" insetsRight="0" anchor="10" weightX="0.0" weightY="0.0"/>
            </Constraint>
          </Constraints>
        </Component>
        <Component class="javax.swing.JLabel" name="lblBaud">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="10" style="0"/>
            </Property>
            <Property name="horizontalAlignment" type="int" value="11"/>
            <Property name="text" type="java.lang.String" value="BaudRate"/>
            <Property name="toolTipText" type="java.lang.String" value="Baud rate"/>
            <Property name="border" type="javax.swing.border.Border" editor="org.netbeans.modules.form.editors2.BorderEditor">
              <Border info="org.netbeans.modules.form.compat2.border.BevelBorderInfo">
                <BevelBorder bevelType="1"/>
              </Border>
            </Property>
            <Property name="minimumSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
              <Dimension value="[54, 16]"/>
            </Property>
            <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
              <Dimension value="[54, 16]"/>
            </Property>
          </Properties>
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout" value="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout$GridBagConstraintsDescription">
              <GridBagConstraints gridX="-1" gridY="-1" gridWidth="1" gridHeight="1" fill="1" ipadX="0" ipadY="0" insetsTop="0" insetsLeft="0" insetsBottom="0" insetsRight="0" anchor="10" weightX="0.0" weightY="0.0"/>
            </Constraint>
          </Constraints>
        </Component>
        <Component class="javax.swing.JComboBox" name="cbCnvSystem">
          <Properties>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="10" style="0"/>
            </Property>
            <Property name="model" type="javax.swing.ComboBoxModel" editor="org.netbeans.modules.form.editors2.ComboBoxModelEditor">
              <StringArray count="2">
                <StringItem index="0" value="Metric"/>
                <StringItem index="1" value="Imperial"/>
              </StringArray>
            </Property>
            <Property name="toolTipText" type="java.lang.String" value="Select conversion system"/>
            <Property name="border" type="javax.swing.border.Border" editor="org.netbeans.modules.form.editors2.BorderEditor">
              <Border info="org.netbeans.modules.form.compat2.border.BevelBorderInfo">
                <BevelBorder bevelType="1"/>
              </Border>
            </Property>
            <Property name="preferredSize" type="java.awt.Dimension" editor="org.netbeans.beaninfo.editors.DimensionEditor">
              <Dimension value="[71, 23]"/>
            </Property>
          </Properties>
          <Events>
            <EventHandler event="itemStateChanged" listener="java.awt.event.ItemListener" parameters="java.awt.event.ItemEvent" handler="cbCnvSystemItemStateChanged"/>
          </Events>
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout" value="org.netbeans.modules.form.compat2.layouts.DesignGridBagLayout$GridBagConstraintsDescription">
              <GridBagConstraints gridX="-1" gridY="-1" gridWidth="1" gridHeight="1" fill="1" ipadX="0" ipadY="0" insetsTop="0" insetsLeft="0" insetsBottom="0" insetsRight="0" anchor="10" weightX="0.0" weightY="0.0"/>
            </Constraint>
          </Constraints>
        </Component>
      </SubComponents>
    </Container>
    <Container class="javax.swing.JPanel" name="panHeader">
      <Constraints>
        <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout" value="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout$BorderConstraintsDescription">
          <BorderConstraints direction="North"/>
        </Constraint>
      </Constraints>

      <Layout class="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout"/>
      <SubComponents>
        <Container class="javax.swing.JToolBar" name="tbMain">
          <Properties>
            <Property name="floatable" type="boolean" value="false"/>
            <Property name="rollover" type="boolean" value="true"/>
            <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
              <Font name="Dialog" size="10" style="0"/>
            </Property>
          </Properties>
          <Constraints>
            <Constraint layoutClass="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout" value="org.netbeans.modules.form.compat2.layouts.DesignBorderLayout$BorderConstraintsDescription">
              <BorderConstraints direction="North"/>
            </Constraint>
          </Constraints>

          <Layout class="org.netbeans.modules.form.compat2.layouts.DesignBoxLayout"/>
          <SubComponents>
            <Component class="javax.swing.JButton" name="btnLoad">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Dialog" size="10" style="0"/>
                </Property>
                <Property name="icon" type="javax.swing.Icon" editor="org.netbeans.modules.form.editors2.IconEditor">
                  <Image iconType="3" name="/com.fr3ts0n.ois/fileopen.png"/>
                </Property>
                <Property name="toolTipText" type="java.lang.String" value="Load measurements"/>
                <Property name="borderPainted" type="boolean" value="false"/>
                <Property name="focusable" type="boolean" value="false"/>
                <Property name="horizontalTextPosition" type="int" value="0"/>
                <Property name="verticalTextPosition" type="int" value="3"/>
              </Properties>
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="miLoadActionPerformed"/>
              </Events>
            </Component>
            <Component class="javax.swing.JButton" name="btnSave">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Dialog" size="10" style="0"/>
                </Property>
                <Property name="icon" type="javax.swing.Icon" editor="org.netbeans.modules.form.editors2.IconEditor">
                  <Image iconType="3" name="/com.fr3ts0n.ois/filesaveas.png"/>
                </Property>
                <Property name="toolTipText" type="java.lang.String" value="Save  measurements"/>
                <Property name="borderPainted" type="boolean" value="false"/>
                <Property name="focusable" type="boolean" value="false"/>
                <Property name="horizontalTextPosition" type="int" value="0"/>
                <Property name="verticalTextPosition" type="int" value="3"/>
              </Properties>
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="miSaveActionPerformed"/>
              </Events>
            </Component>
            <Component class="javax.swing.JToolBar$Separator" name="jSeparator1">
            </Component>
            <Component class="javax.swing.JButton" name="btnConnect">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Dialog" size="10" style="0"/>
                </Property>
                <Property name="icon" type="javax.swing.Icon" editor="org.netbeans.modules.form.editors2.IconEditor">
                  <Image iconType="3" name="/com.fr3ts0n.ois/connect_established.png"/>
                </Property>
                <Property name="toolTipText" type="java.lang.String" value="Start communication"/>
                <Property name="borderPainted" type="boolean" value="false"/>
                <Property name="focusable" type="boolean" value="false"/>
                <Property name="horizontalTextPosition" type="int" value="0"/>
                <Property name="verticalTextPosition" type="int" value="3"/>
              </Properties>
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="connectEcu"/>
              </Events>
            </Component>
            <Component class="javax.swing.JButton" name="btnStop">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Dialog" size="10" style="0"/>
                </Property>
                <Property name="icon" type="javax.swing.Icon" editor="org.netbeans.modules.form.editors2.IconEditor">
                  <Image iconType="3" name="/com.fr3ts0n.ois/no.png"/>
                </Property>
                <Property name="toolTipText" type="java.lang.String" value="Stop communication"/>
                <Property name="borderPainted" type="boolean" value="false"/>
                <Property name="focusable" type="boolean" value="false"/>
                <Property name="horizontalTextPosition" type="int" value="0"/>
                <Property name="verticalTextPosition" type="int" value="3"/>
              </Properties>
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="miCommStopActionPerformed"/>
              </Events>
            </Component>
            <Component class="javax.swing.JButton" name="btnConfig">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Dialog" size="10" style="0"/>
                </Property>
                <Property name="icon" type="javax.swing.Icon" editor="org.netbeans.modules.form.editors2.IconEditor">
                  <Image iconType="3" name="/com.fr3ts0n.ois/fileexport.png"/>
                </Property>
                <Property name="toolTipText" type="java.lang.String" value="Serial port configuratopn"/>
                <Property name="borderPainted" type="boolean" value="false"/>
                <Property name="focusable" type="boolean" value="false"/>
                <Property name="horizontalTextPosition" type="int" value="0"/>
                <Property name="verticalTextPosition" type="int" value="3"/>
              </Properties>
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="miCommConfigureActionPerformed"/>
              </Events>
            </Component>
            <Component class="javax.swing.JToolBar$Separator" name="jSeparator2">
            </Component>
            <Component class="javax.swing.JComboBox" name="cbAddress">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Dialog" size="10" style="0"/>
                </Property>
                <Property name="toolTipText" type="java.lang.String" value="Select device/address"/>
              </Properties>
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="cbAddressActionPerformed"/>
              </Events>
            </Component>
            <Component class="javax.swing.JToolBar$Separator" name="jSeparator3">
            </Component>
            <Component class="javax.swing.JComboBox" name="cbFrameNum">
              <Properties>
                <Property name="font" type="java.awt.Font" editor="org.netbeans.beaninfo.editors.FontEditor">
                  <Font name="Dialog" size="10" style="0"/>
                </Property>
                <Property name="model" type="javax.swing.ComboBoxModel" editor="org.netbeans.modules.form.editors2.ComboBoxModelEditor">
                  <StringArray count="4">
                    <StringItem index="0" value="Item 1"/>
                    <StringItem index="1" value="Item 2"/>
                    <StringItem index="2" value="Item 3"/>
                    <StringItem index="3" value="Item 4"/>
                  </StringArray>
                </Property>
                <Property name="toolTipText" type="java.lang.String" value="select Frame number to display"/>
              </Properties>
              <Events>
                <EventHandler event="actionPerformed" listener="java.awt.event.ActionListener" parameters="java.awt.event.ActionEvent" handler="cbFrameNumActionPerformed"/>
              </Events>
            </Component>
          </SubComponents>
        </Container>
      </SubComponents>
    </Container>
  </SubComponents>
</Form>
