"""
Enhanced API Routes
New routes for expert mode, live data monitoring, and AI-enhanced diagnostics
"""
import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from .models import *
from ..expert_mode.expert_manager import expert_manager, ExpertOperation
from ..live_data.fuel_mixture_analyzer import fuel_mixture_analyzer, MixtureStatus
from ..ai_engine.gemini_integration import gemini_integration
from ..database.database import db_manager

# Import these conditionally to avoid circular imports
enhanced_monitor = None
connection_manager = None

try:
    from ..live_data.enhanced_monitor import enhanced_monitor
    from ..obd_interface.connection_manager import connection_manager
except ImportError:
    pass

logger = logging.getLogger(__name__)

enhanced_router = APIRouter(prefix="/api/v1/enhanced", tags=["enhanced"])


# Expert Mode Models
class ExpertModeRequest(BaseModel):
    """Expert mode activation request"""
    unlock_code: str = Field(..., description="Expert mode unlock code")
    user_identifier: str = Field(..., description="User identifier (IP, device ID, etc.)")
    requested_permissions: Optional[List[str]] = Field(default=None, description="Requested permissions")


class ExpertOperationRequest(BaseModel):
    """Expert operation request"""
    session_token: str = Field(..., description="Expert session token")
    operation: str = Field(..., description="Operation to perform")
    parameters: Optional[Dict[str, Any]] = Field(default=None, description="Operation parameters")
    confirm: bool = Field(default=False, description="Confirmation flag for dangerous operations")


# Live Data Models
class LiveDataRequest(BaseModel):
    """Live data monitoring request"""
    sensors: Optional[List[str]] = Field(default=None, description="Specific sensors to monitor")
    include_fuel_analysis: bool = Field(default=True, description="Include fuel mixture analysis")
    include_ai_analysis: bool = Field(default=True, description="Include AI analysis for concerning readings")
    update_interval: float = Field(default=0.1, description="Update interval in seconds")


class FuelMixtureResponse(BaseModel):
    """Fuel mixture analysis response"""
    status: str
    short_term_trim_bank1: float
    long_term_trim_bank1: float
    oxygen_sensor_bank1: float
    air_fuel_ratio: Optional[float]
    efficiency_rating: float
    recommendations: List[str]
    ai_analysis: Optional[str]
    confidence_score: float
    timestamp: str


# Safety Mode Endpoints
@enhanced_router.get("/safety-mode/status")
async def get_safety_mode_status():
    """
    Get current safety mode status
    """
    try:
        # Check if expert mode is active
        active_sessions = await expert_manager._count_active_sessions()
        is_expert_mode = active_sessions > 0

        return {
            "is_safe_mode": not is_expert_mode,
            "expert_mode_available": True,
            "restricted_operations": [
                "ECU Write Operations",
                "Adaptation Reset",
                "Calibration Changes",
                "Security Access"
            ] if not is_expert_mode else [],
            "warnings": [
                "Safe mode prevents potentially dangerous operations",
                "Expert mode required for write operations"
            ] if not is_expert_mode else [
                "Expert mode active - write operations enabled",
                "Use caution with all operations"
            ]
        }

    except Exception as e:
        logger.error(f"Error getting safety mode status: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Expert Mode Endpoints
@enhanced_router.post("/expert-mode/request")
async def request_expert_mode(request: ExpertModeRequest):
    """
    Request expert mode access (frontend compatible endpoint)
    """
    try:
        # Convert permission strings to enum
        permissions = []
        if request.requested_permissions:
            for perm in request.requested_permissions:
                try:
                    permissions.append(ExpertOperation(perm))
                except ValueError:
                    logger.warning(f"Invalid permission requested: {perm}")

        result = await expert_manager.request_expert_mode(
            request.user_identifier,
            request.unlock_code,
            permissions
        )

        return result

    except Exception as e:
        logger.error(f"Error requesting expert mode: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@enhanced_router.post("/expert-mode/exit")
async def exit_expert_mode():
    """
    Exit expert mode (revoke all active sessions)
    """
    try:
        # Get all active sessions and revoke them
        revoked_count = 0
        for session_token in list(expert_manager.active_sessions.keys()):
            success = await expert_manager.revoke_expert_session(session_token)
            if success:
                revoked_count += 1

        return {
            "success": True,
            "message": f"Expert mode deactivated ({revoked_count} sessions revoked)",
            "revoked_sessions": revoked_count
        }

    except Exception as e:
        logger.error(f"Error exiting expert mode: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@enhanced_router.post("/expert/activate")
async def activate_expert_mode(request: ExpertModeRequest):
    """
    Activate expert mode with unlock code
    """
    try:
        # Convert permission strings to enum
        permissions = []
        if request.requested_permissions:
            for perm in request.requested_permissions:
                try:
                    permissions.append(ExpertOperation(perm))
                except ValueError:
                    logger.warning(f"Invalid permission requested: {perm}")
        
        result = await expert_manager.request_expert_mode(
            request.user_identifier,
            request.unlock_code,
            permissions
        )
        
        return {
            "success": result["success"],
            "data": result if result["success"] else None,
            "error": result.get("error") if not result["success"] else None
        }
        
    except Exception as e:
        logger.error(f"Error activating expert mode: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@enhanced_router.post("/expert/verify")
async def verify_expert_session(session_token: str):
    """
    Verify expert mode session
    """
    try:
        session = await expert_manager.verify_expert_session(session_token)
        
        if session:
            return {
                "success": True,
                "data": {
                    "valid": True,
                    "expires_at": session.expires_at.isoformat(),
                    "permissions": expert_manager._get_session_permissions(session),
                    "operations_count": len(session.operations_performed or [])
                }
            }
        else:
            return {
                "success": True,
                "data": {"valid": False}
            }
            
    except Exception as e:
        logger.error(f"Error verifying expert session: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@enhanced_router.post("/expert/operation")
async def perform_expert_operation(request: ExpertOperationRequest):
    """
    Perform expert mode operation
    """
    try:
        # Verify session
        session = await expert_manager.verify_expert_session(request.session_token)
        if not session:
            raise HTTPException(status_code=401, detail="Invalid or expired expert session")
        
        # Convert operation string to enum
        try:
            operation = ExpertOperation(request.operation)
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid operation: {request.operation}")
        
        # Check permissions
        permission_check = await expert_manager.check_operation_permission(
            request.session_token, operation
        )
        
        if not permission_check["allowed"]:
            raise HTTPException(status_code=403, detail=permission_check["reason"])
        
        # Check confirmation for dangerous operations
        if permission_check.get("requires_confirmation") and not request.confirm:
            return {
                "success": False,
                "requires_confirmation": True,
                "warning": f"Operation {operation.value} requires confirmation",
                "operation_details": request.parameters
            }
        
        # Perform operation
        result = await _execute_expert_operation(operation, request.parameters)
        
        # Log operation
        await expert_manager.log_operation(
            request.session_token,
            operation,
            {
                "parameters": request.parameters,
                "result": result,
                "timestamp": datetime.now().isoformat()
            }
        )
        
        return {
            "success": True,
            "data": result,
            "operation": operation.value
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error performing expert operation: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@enhanced_router.post("/expert/revoke")
async def revoke_expert_session(session_token: str):
    """
    Revoke expert mode session
    """
    try:
        success = await expert_manager.revoke_expert_session(session_token)
        
        return {
            "success": success,
            "message": "Expert session revoked" if success else "Session not found"
        }
        
    except Exception as e:
        logger.error(f"Error revoking expert session: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# Live Data Endpoints
@enhanced_router.post("/live-data/start")
async def start_live_monitoring(request: LiveDataRequest):
    """
    Start live data monitoring
    """
    try:
        # Check if OBD connection is available
        if not connection_manager or not connection_manager.is_connected():
            raise HTTPException(status_code=400, detail="OBD connection not available")
        
        # Start enhanced monitoring
        if not enhanced_monitor:
            raise HTTPException(status_code=500, detail="Enhanced monitor not available")

        success = await enhanced_monitor.start_monitoring(
            sensors=request.sensors,
            update_interval=request.update_interval
        )
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to start live monitoring")
        
        return {
            "success": True,
            "message": "Live monitoring started",
            "monitoring_sensors": request.sensors or "all_available",
            "update_interval": request.update_interval
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting live monitoring: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@enhanced_router.get("/live-data/current")
async def get_current_live_data():
    """
    Get current live sensor data
    """
    try:
        if not enhanced_monitor or not enhanced_monitor.is_monitoring:
            raise HTTPException(status_code=400, detail="Live monitoring not active")
        
        # Get current readings
        current_readings = enhanced_monitor.get_current_readings()
        
        # Get fuel mixture analysis
        fuel_analysis = None
        if current_readings:
            try:
                # Convert readings to format expected by fuel analyzer
                sensor_data = {}
                for sensor_type, reading in current_readings.items():
                    sensor_data[sensor_type.value] = type('OBDParam', (), {
                        'value': reading.value,
                        'unit': reading.unit,
                        'timestamp': reading.timestamp
                    })()
                
                fuel_reading = await fuel_mixture_analyzer.analyze_mixture(sensor_data)
                fuel_analysis = {
                    "status": fuel_reading.status.value,
                    "short_term_trim_bank1": fuel_reading.short_term_trim_bank1,
                    "long_term_trim_bank1": fuel_reading.long_term_trim_bank1,
                    "oxygen_sensor_bank1": fuel_reading.oxygen_sensor_bank1,
                    "air_fuel_ratio": fuel_reading.air_fuel_ratio,
                    "efficiency_rating": fuel_reading.efficiency_rating,
                    "recommendations": fuel_reading.recommendations,
                    "ai_analysis": fuel_reading.ai_analysis,
                    "confidence_score": fuel_reading.confidence_score,
                    "timestamp": fuel_reading.timestamp.isoformat()
                }
            except Exception as e:
                logger.error(f"Error analyzing fuel mixture: {e}")
        
        # Format response
        formatted_readings = {}
        for sensor_type, reading in current_readings.items():
            formatted_readings[sensor_type.value] = {
                "value": reading.value,
                "unit": reading.unit,
                "status": reading.status,
                "trend": reading.trend,
                "timestamp": reading.timestamp.isoformat(),
                "quality": reading.quality,
                "analysis": reading.analysis
            }
        
        return {
            "success": True,
            "data": {
                "sensors": formatted_readings,
                "fuel_mixture": fuel_analysis,
                "monitoring_stats": enhanced_monitor.get_monitoring_stats(),
                "timestamp": datetime.now().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting live data: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@enhanced_router.get("/live-data/fuel-trend")
async def get_fuel_mixture_trend(minutes: int = 5):
    """
    Get fuel mixture trend over specified time period
    """
    try:
        trend_data = fuel_mixture_analyzer.get_mixture_trend(minutes)
        
        return {
            "success": True,
            "data": trend_data
        }
        
    except Exception as e:
        logger.error(f"Error getting fuel trend: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@enhanced_router.post("/live-data/stop")
async def stop_live_monitoring():
    """
    Stop live data monitoring
    """
    try:
        if enhanced_monitor:
            await enhanced_monitor.stop_monitoring()
        
        return {
            "success": True,
            "message": "Live monitoring stopped"
        }
        
    except Exception as e:
        logger.error(f"Error stopping live monitoring: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


# AI Enhancement Endpoints
@enhanced_router.post("/ai/analyze-live-data")
async def analyze_live_data_with_ai(
    sensor_data: Dict[str, Any],
    vehicle_info: Optional[Dict[str, Any]] = None
):
    """
    Get AI analysis for live sensor data
    """
    try:
        if not gemini_integration.is_initialized:
            raise HTTPException(status_code=400, detail="AI engine not available")
        
        response = await gemini_integration.explain_live_data(
            sensor_data,
            vehicle_info or {},
            "Live sensor data analysis"
        )
        
        return {
            "success": True,
            "data": {
                "analysis": response.content,
                "model_used": response.model_used,
                "confidence_score": response.confidence_score,
                "processing_time": response.processing_time
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing live data with AI: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@enhanced_router.post("/ai/repair-recommendations")
async def get_ai_repair_recommendations(
    diagnostic_context: Dict[str, Any]
):
    """
    Get AI-powered repair recommendations
    """
    try:
        if not gemini_integration.is_initialized:
            raise HTTPException(status_code=400, detail="AI engine not available")
        
        response = await gemini_integration.generate_repair_recommendations(diagnostic_context)
        
        return {
            "success": True,
            "data": {
                "recommendations": response.content,
                "model_used": response.model_used,
                "confidence_score": response.confidence_score,
                "processing_time": response.processing_time
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting repair recommendations: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


async def _execute_expert_operation(operation: ExpertOperation, parameters: Optional[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Execute expert mode operation
    """
    if operation == ExpertOperation.CLEAR_DTC:
        # Clear DTC codes
        if connection_manager and connection_manager.is_connected():
            success = await connection_manager.clear_dtc_codes()
            return {"operation": "clear_dtc", "success": success}
        else:
            raise HTTPException(status_code=400, detail="OBD connection not available")
    
    elif operation == ExpertOperation.WRITE_ECU:
        # ECU write operation (very dangerous)
        if not parameters or "ecu_address" not in parameters or "data" not in parameters:
            raise HTTPException(status_code=400, detail="ECU address and data required")

        # This would be implemented with proper safety checks
        return {
            "operation": "write_ecu",
            "warning": "ECU write operations not implemented for safety",
            "parameters": parameters
        }
    
    elif operation == ExpertOperation.RESET_ADAPTATIONS:
        # Reset ECU adaptations
        return {
            "operation": "reset_adaptations",
            "warning": "Adaptation reset not implemented",
            "note": "This would reset learned parameters"
        }
    
    elif operation == ExpertOperation.PERFORM_TESTS:
        # Perform diagnostic tests
        if parameters and "test_type" in parameters:
            test_type = parameters["test_type"]
            return {
                "operation": "perform_test",
                "test_type": test_type,
                "result": f"Test {test_type} would be performed here"
            }
        else:
            return {
                "operation": "perform_test",
                "available_tests": ["actuator_test", "sensor_test", "system_test"]
            }
    
    else:
        return {"operation": operation.value, "status": "not_implemented"}
